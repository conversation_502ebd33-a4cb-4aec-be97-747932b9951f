{"name": "cashier", "version": "0.0.0", "license": "ISC", "scripts": {"dev": "vite", "build": "vite build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^0.27.2", "element-plus": "^2.8.5", "pinia": "^2.0.18", "vue": "^3.0.4", "vue-i18n": "^10.0.4", "vue-router": "^4.0.16"}, "devDependencies": {"@vue/compiler-sfc": "^3.0.4", "sass": "^1.53.0", "sass-loader": "^13.0.2", "typescript": "^4.7.4", "unplugin-auto-import": "^0.10.3", "unplugin-vue-components": "^0.21.2", "vite": "^1.0.0-rc.13"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}