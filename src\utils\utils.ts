import { reactive } from "vue";

interface currencySymbolType {
value: number,
text: string
}
/*
人民币￥
台币NT$
美元$
越南盾₫
泰铢฿
印度₹
马来西亚RM*/
const currencySymbol: currencySymbolType[] = reactive([
    {value: 0, text: '¥'},
    {value: 1, text: 'NT$'},
    {value: 2, text: '$'},
    {value: 3, text: '₫'},
    {value: 4, text: '฿'},
    {value: 5, text: '₹'},
    {value: 6, text: 'RM'},
]);
function gcs() {
    const k = 4 // 默认泰铢;
    return (currencySymbol.filter(c => c.value == k)[0]?.text || '฿') + ' ';
}
const $cache:any = {};
function debounce(func: Function, wait = 200, id = '') {
    let debounceTimerId = `debounceTimerId_${id}`;
    console.log(debounceTimerId, $cache)
    if ($cache[debounceTimerId]) {
        console.log('debounce clearTimeout')
        clearTimeout($cache[debounceTimerId])
        Reflect.deleteProperty($cache, debounceTimerId)
    }
    $cache[debounceTimerId] = setTimeout(func, wait)
}
function dateFormat(s: any) {
    if(!s) return '';
    if(new String(s).includes('-')) return s;
    const date = new Date(s * 1000);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}
function dateTimeFormat(s: any) {
    if(!s) return '';
    if(new String(s).includes('-')) return s;
    const date = new Date(s * 1000);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
function secondsFormat(seconds: any) {
    let hours = Math.floor(seconds / 3600);
    let remainingSeconds = seconds % 3600;
    let minutes = Math.floor(remainingSeconds / 60);
    let sec = remainingSeconds % 60;  
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(sec).padStart(2, '0')}`;
  }
export {
    gcs,
    debounce,
    dateFormat,
    dateTimeFormat,
    secondsFormat
}