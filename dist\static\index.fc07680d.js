let e=document.createElement("style");e.innerHTML=".el-radio__label{font-size:20px}.el-checkbox__label{font-size:16px}.el-radio{height:auto;white-space:pre-wrap}.el-card.is-always-shadow{box-shadow:none}.add_member .el-input,.add_member .el-radio-group{height:50px;font-size:14px;width:calc(100% - 100px)}.add_member .el-radio{margin:0}.add_member .el-radio.el-radio--large{height:auto}.bg-green[data-v-3c13e60b]{background-color:#15bc84}.border-green[data-v-3c13e60b]{border:1px solid #15bc84}.bg-red[data-v-3c13e60b]{background-color:#b60707}.border-red[data-v-3c13e60b]{border:1px solid #b60707}.orderInfoRemark .remarks_item[data-v-3c13e60b]{width:100%;display:flex;align-items:center;justify-content:space-between}.orderInfoRemark .remarks_item label[data-v-3c13e60b]{display:inline-block;width:70px}.orderInfoRemark .remarks_item span[data-v-3c13e60b]{flex:1}.content[data-v-3c13e60b]{position:relative;height:100%;font-size:14px}.table_list[data-v-3c13e60b]{display:flex;column-gap:20px;row-gap:20px;flex-wrap:wrap;padding:0 20px}.table_list .table_item[data-v-3c13e60b]{width:150px;height:150px;color:#fff;border-radius:10px;display:flex;flex-direction:column;justify-content:center;align-items:center;cursor:pointer}.table_list .table_item[data-v-3c13e60b]:hover{opacity:.8}.inventory[data-v-3c13e60b]{position:relative;width:520px;height:100%;flex-direction:column;background:#fff}.inventory .inventory_alert[data-v-3c13e60b]{position:absolute;top:0;bottom:0;width:100%;background:rgba(255,255,255,.7);z-index:2;cursor:not-allowed}.inventory .inventory_head[data-v-3c13e60b]{color:#333;height:50px;border-bottom:1px solid #f2f2f2;padding:0 16px;font-size:16px}.inventory .inventory_head .inventory_title[data-v-3c13e60b]{font-weight:700}.inventory .body[data-v-3c13e60b]{overflow:auto}.inventory .body_null[data-v-3c13e60b]{margin:50px 0}.inventory .body_icon[data-v-3c13e60b]{height:110px;width:110px;display:block;margin:0 auto}.inventory .body_title[data-v-3c13e60b]{text-align:center;font-size:14px;font-weight:500;color:#b2b2b2;margin-top:30px}.inventory .module[data-v-3c13e60b]{padding:13px;border-bottom:1px solid #f2f2f2;box-sizing:border-box}.inventory .module .module_image[data-v-3c13e60b]{width:68px;height:68px;border-radius:4px;margin-right:10px}.inventory .module .module_image .image-slot[data-v-3c13e60b]{display:flex;justify-content:center;align-items:center;width:100%;height:100%;background:var(--el-fill-color-light);color:var(--el-text-color-secondary);font-size:30px}.inventory .module .module_image .image-slot .el-icon[data-v-3c13e60b]{font-size:30px}.inventory .module .module_title[data-v-3c13e60b]{color:#333}.inventory .module .module_unit[data-v-3c13e60b]{font-size:14px;color:#333;margin:5px 0}.inventory .module .module_text[data-v-3c13e60b]{width:330px}.inventory .module .module_up[data-v-3c13e60b]{margin-left:14px;color:#4476ff;cursor:pointer}.inventory .module .module_price[data-v-3c13e60b]{font-size:16px}.inventory .module .module_opt[data-v-3c13e60b]{width:140px;border:1px solid #dcdee2;border-radius:4px}.inventory .module .module_icon[data-v-3c13e60b]{color:#808695;cursor:pointer;padding:0;width:30px;background-color:#f5f6fa}.inventory .module .module_input[data-v-3c13e60b]{width:100%;height:30px;padding:0 7px;font-size:16px;border-right:1px solid #dcdee2;border-left:1px solid #dcdee2;border-top:none;border-bottom:none;color:#333;text-align:center;background-color:#fff;background-image:none;position:relative}.inventory .module .module_total[data-v-3c13e60b]{font-size:16px;text-align:right}.inventory .order_remarks[data-v-3c13e60b]{display:flex;justify-content:space-between;padding:15px;border-top:1px solid #f2f2f2;align-items:center;color:#666}.inventory .group[data-v-3c13e60b]{padding:8px;border-top:1px solid #f2f2f2;display:flex;justify-content:space-between;flex-wrap:wrap;row-gap:10px}.inventory .group .group_btn[data-v-3c13e60b]{height:48px;font-size:16px;border-radius:4px;cursor:pointer;width:23%}hr[data-v-3c13e60b]{border:none;height:1px;background-color:#ccc}.order_info[data-v-3c13e60b]{padding:0 20px}.order_info .order_info_item[data-v-3c13e60b]{padding:5px 0}.order_info .order_info_item label[data-v-3c13e60b]{display:inline-block;width:150px}.operate[data-v-3c13e60b]{position:relative;margin-left:20px;height:100%;background:#fff;flex-direction:column}.operate .operate_table[data-v-3c13e60b]{padding:0 16px;font-size:16px;border-bottom:1px solid #f2f2f2}.operate .operate_item[data-v-3c13e60b]{position:relative;padding:14px 16px;color:#333;margin-right:16px;cursor:pointer;text-align:center;border-bottom:2px solid #fff}.operate .operate_num[data-v-3c13e60b]{position:absolute;right:0;top:3px;padding:1px 5.5px;font-size:14px;color:#fff;border-radius:100px}.operate .operate_body[data-v-3c13e60b]{position:relative}.shop .shop_body[data-v-3c13e60b]{height:100%;padding:16px;align-items:flex-start;box-sizing:border-box;flex-direction:column}.shop .shop_list[data-v-3c13e60b]{position:relative;width:100%;overflow:auto}.shop .shop_more[data-v-3c13e60b]{font-size:14px;color:#999;text-align:center;padding-bottom:10px}.shop .shop_table[data-v-3c13e60b]{width:100px;height:100%;overflow-y:auto;border-left:1px solid #f2f2f2}.shop .shop_item[data-v-3c13e60b]{text-align:center;height:40px;line-height:40px;cursor:pointer;font-size:16px;padding:0 6px}.shop .shop_search[data-v-3c13e60b]{position:relative;width:100%;box-sizing:border-box;margin-bottom:25px}.shop .shop_icon[data-v-3c13e60b]{position:absolute;top:0;bottom:0;left:10px;margin:auto 0}.shop .shop_input[data-v-3c13e60b]{color:#333;padding:4px 7px 4px 40px;border:1px solid #dcdee2;background:#f2f3f5;border-radius:4px;box-sizing:border-box;height:40px}.shop .shop_module[data-v-3c13e60b]{border-radius:4px;border:1px solid #ededed;margin:0 15px 15px 0;box-sizing:border-box;padding:12px;position:relative;cursor:pointer;width:275px;background:#fff}.shop .shop_img[data-v-3c13e60b]{width:68px;height:68px;margin-right:14px;border-radius:4px}.shop .shop_img .image-slot[data-v-3c13e60b]{display:flex;justify-content:center;align-items:center;width:100%;height:100%;background:var(--el-fill-color-light);color:var(--el-text-color-secondary);font-size:30px}.shop .shop_img .image-slot .el-icon[data-v-3c13e60b]{font-size:30px}.shop .shop_title[data-v-3c13e60b]{color:#333}.shop .shop_data[data-v-3c13e60b]{margin-top:12px;font-size:14px}.shop .shop_stock[data-v-3c13e60b]{color:#999}.shop .shop_notice[data-v-3c13e60b]{padding:150px 0 0;width:100%}.shop .shop_notice img[data-v-3c13e60b]{height:110px;width:110px;display:block;margin:0 auto}.shop .shop_notice .text[data-v-3c13e60b]{text-align:center;font-size:14px;font-weight:500;color:#b2b2b2;margin-top:30px}.btn_div .memberDetail_btn[data-v-3c13e60b]{position:absolute;top:36px;right:20px;border:1px solid;padding:0 15px;height:36px;line-height:36px;font-size:14px;border-radius:4px;cursor:pointer}.store_val_pay[data-v-3c13e60b]{padding:20px 50px}.store_val_pay label[data-v-3c13e60b]{padding:0 20px}.store_val_pay .pay_type_list[data-v-3c13e60b]{display:flex;padding:20px;flex-wrap:wrap;justify-content:space-between}.store_val_pay .pay_type_list .pay_type_item[data-v-3c13e60b]{width:24%;aspect-ratio:3/2;border-radius:5px;display:flex;align-items:center;justify-content:center;cursor:pointer;background-color:#eee}.add_member[data-v-3c13e60b]{width:400px}.add_member .add_member_title[data-v-3c13e60b]{text-align:center;font-size:1.2em;padding:20px}.add_member .add_member_ipt[data-v-3c13e60b]{padding:15px 0}.add_member .add_member_ipt label[data-v-3c13e60b]{display:inline-block;width:100px;font-size:14px;color:#666;margin-bottom:10px}.add_member .add_member_ipt input[data-v-3c13e60b]{height:50px;font-size:14px;border:1px solid #dcdee2;border-radius:4px;color:#333;box-sizing:border-box;width:calc(100% - 100px);padding:6px 18px}.add_member .add_member_ipt input[data-v-3c13e60b]:focus{border:1px solid #409eff;outline:0}.add_member .add_member_ipt input[data-v-3c13e60b]:focus-visible{border:1px solid #409eff}.add_member .add_member_ipt input.mobile[data-v-3c13e60b]{-moz-appearance:textfield}.add_member .add_member_ipt input.mobile[data-v-3c13e60b]::-webkit-inner-spin-button,.add_member .add_member_ipt input.mobile[data-v-3c13e60b]::-webkit-outer-spin-button{-webkit-appearance:none;margin:0}.add_member .add_member_btn_div[data-v-3c13e60b]{display:flex;justify-content:space-between;padding:20px 0}.add_member .add_member_btn_div .add_member_btn[data-v-3c13e60b]{height:48px;font-size:16px;border-radius:4px;cursor:pointer;width:40%}.key[data-v-3c13e60b]{max-width:580px;height:386px;position:absolute;left:0;right:0;top:0;bottom:0;margin:auto}.key .key_title[data-v-3c13e60b]{font-size:16px;color:#666;line-height:20px;padding-bottom:15px}.key .key_text[data-v-3c13e60b]{color:#999;font-size:14px;margin:5px 0 0;padding-bottom:15px}.key .key_data[data-v-3c13e60b]{position:relative}.key .key_unit[data-v-3c13e60b]{position:absolute;top:0;bottom:0;right:0;margin:auto 0;font-size:18px;width:60px;line-height:60px;height:60px}.key .key_input[data-v-3c13e60b]{height:60px;font-size:18px;border:1px solid #dcdee2;border-radius:4px;color:#333;box-sizing:border-box;width:100%;padding:6px 18px}.key .key_module[data-v-3c13e60b]{background:#f5f6fa;border-top:1px solid #dcdee2;border-left:1px solid #dcdee2;margin-top:25px;font-size:20px;cursor:pointer}.key .key_content[data-v-3c13e60b]{width:75%}.key .key_opt[data-v-3c13e60b]{width:25%}.key .key_item[data-v-3c13e60b]{height:66px;color:#333;border-right:1px solid #dcdee2;border-bottom:1px solid #dcdee2;width:33.33%;box-sizing:border-box;text-align:center;line-height:66px;user-select:none}.key .item_active[data-v-3c13e60b]{background:#dadde6}.key .key_row[data-v-3c13e60b]{width:100%}.key .key_confirm[data-v-3c13e60b]{line-height:132px;text-align:center;box-sizing:border-box;color:#fff;border-right:1px solid;border-bottom:1px solid;user-select:none}.take[data-v-3c13e60b]{position:relative;padding:16px;width:100%;height:100%;box-sizing:border-box}.take .take_module[data-v-3c13e60b]{height:100%;width:100%;overflow-y:scroll}.take .take_list[data-v-3c13e60b]{padding-top:16px}.take .take_list[data-v-3c13e60b]:first-child{padding-top:0}.take .take_date[data-v-3c13e60b]{color:#333}.take .take_item[data-v-3c13e60b]{width:225px;background:#f5f6fa;border-radius:4px;margin-top:20px;margin-right:20px;padding:16px;position:relative;box-sizing:border-box}.take .take_title[data-v-3c13e60b]{color:#333}.take .take_data[data-v-3c13e60b]{margin-top:30px;font-size:14px}.take .take_num[data-v-3c13e60b]{color:#333}.take .take_opt[data-v-3c13e60b]{margin-top:25px;padding-bottom:20px;border-bottom:1px solid #f0f0f0}.take .take_null[data-v-3c13e60b]{margin-top:250px}.take .take_icon[data-v-3c13e60b]{height:110px;width:110px;display:block;margin:0 auto}.take .take_text[data-v-3c13e60b]{text-align:center;font-size:14px;font-weight:500;color:#b2b2b2;margin-top:30px}.take .take_order[data-v-3c13e60b]{color:#333}.take .take_btn[data-v-3c13e60b]{font-size:14px;margin-left:15px;height:32px;padding:0 15px;text-align:center;line-height:32px;border-radius:5px;cursor:pointer;border:1px solid}.update[data-v-3c13e60b]{position:relative;height:100%}.update .update_body[data-v-3c13e60b]{padding:16px}.update .update_module[data-v-3c13e60b]{width:276px;border-radius:4px;border:1px solid #ededed;padding:12px;position:relative;color:#333;cursor:pointer;background:#fff}.update .update_img[data-v-3c13e60b]{width:68px;height:68px;margin-right:14px;border-radius:4px}.update .update_img .image-slot[data-v-3c13e60b]{display:flex;justify-content:center;align-items:center;width:100%;height:100%;background:var(--el-fill-color-light);color:var(--el-text-color-secondary);font-size:30px}.update .update_img .image-slot .el-icon[data-v-3c13e60b]{font-size:30px}.update .update_data[data-v-3c13e60b]{margin-top:12px;font-size:14px}.update .update_stock[data-v-3c13e60b]{color:#999}.update .update_title[data-v-3c13e60b]{color:#333;line-height:50px;padding:0 16px;font-weight:700;border-bottom:1px solid #f2f2f2}.update .update_icon[data-v-3c13e60b]{cursor:pointer}.update .update_item[data-v-3c13e60b]{padding:30px 10px 0 30px;color:#333}.update .update_lable[data-v-3c13e60b]{width:70px}.update .update_set[data-v-3c13e60b]{position:relative}.update .update_input[data-v-3c13e60b]{width:180px;height:48px;border-radius:5px;padding:0 5px;color:#333;font-size:14px;overflow:hidden;border:1px solid #dcdee2}.update .update_unit[data-v-3c13e60b]{position:absolute;top:0;bottom:0;right:1px;margin:auto 0;height:48px;width:48px;line-height:48px;color:#333;text-align:center;background-color:#f8f8f9;border-left:1px solid #dcdee2}.update .update_opt[data-v-3c13e60b]{position:absolute;padding:8px;width:100%;box-sizing:border-box;bottom:0;border-top:1px solid #f2f2f2}.update .update_opt .update_btn[data-v-3c13e60b]{height:48px;width:90px;font-size:14px;border:1px solid;border-radius:4px;margin-right:10px;cursor:pointer}.update .update_opt .update_btn[data-v-3c13e60b]:last-child{color:#fff;margin-right:0}.el-radio[data-v-3c13e60b]{margin:0 20px 20px 0}.memberDetail[data-v-3c13e60b]{position:relative;padding:0 20px;height:100%;width:100%;box-sizing:border-box;overflow-y:auto}.memberDetail .memberDetail_user[data-v-3c13e60b]{display:flex;align-items:center;padding:32px 0}.memberDetail .memberDetail_head[data-v-3c13e60b]{width:48px;height:48px;border-radius:24px;display:block;margin-right:14px}.memberDetail .memberDetail_data[data-v-3c13e60b]{flex:1}.memberDetail .memberDetail_name[data-v-3c13e60b]{color:#333;font-size:16px;display:flex;align-items:center}.memberDetail .memberDetail_phone[data-v-3c13e60b]{font-size:14px;color:#333;margin-top:5px}.memberDetail .memberDetail_tag[data-v-3c13e60b]{margin-left:12px;background:#fef2ed;font-size:10px;cursor:pointer;padding:0 15px;display:flex;align-items:center;line-height:25px;text-align:center}.memberDetail .memberDetail_icon[data-v-3c13e60b]{font-size:15px;color:#ccc;margin-left:5px}.memberDetail .memberDetail_btn[data-v-3c13e60b]{border:1px solid;padding:0 15px;height:36px;line-height:36px;font-size:14px;border-radius:4px;cursor:pointer}.memberDetail .memberDetail_module[data-v-3c13e60b]{background:#f5f6fa;border-radius:4px;color:#333;display:flex;flex-wrap:wrap;padding:10px 0}.memberDetail .memberDetail_item[data-v-3c13e60b]{padding:15px 20px;box-sizing:border-box;width:25%}.memberDetail .memberDetail_item .add_store_val[data-v-3c13e60b]{color:#409eff;margin:25px;cursor:pointer}.memberDetail .memberDetail_item .text[data-v-3c13e60b]{font-size:14px}.memberDetail .memberDetail_item .num[data-v-3c13e60b]{font-size:26px;font-weight:800;margin-top:10px}.memberDetail .memberDetail_item .opt[data-v-3c13e60b]{font-size:14px;margin-left:5px;cursor:pointer;font-weight:400}.memberDetail .memberDetail_title[data-v-3c13e60b]{color:#333;line-height:62px;font-size:14px;font-weight:700}.memberDetail .memberDetail_list[data-v-3c13e60b]{border-radius:5px;background:#f5f6fa;padding:22px;font-size:14px;color:#333}.memberDetail .memberDetail_list .item[data-v-3c13e60b]{position:relative;line-height:32px;font-size:14px;display:flex}.memberDetail .memberDetail_list .title[data-v-3c13e60b]{width:100px;display:flex;justify-content:flex-end}.memberDetail .memberDetail_list .text[data-v-3c13e60b]{position:relative}.pay[data-v-3c13e60b]{position:relative;height:100%}.pay .pay_body[data-v-3c13e60b]{padding:16px;height:100%;box-sizing:border-box}.pay .pay_header[data-v-3c13e60b]{display:flex;align-items:center}.pay .pay_data[data-v-3c13e60b]{width:255px;display:flex;flex-shrink:0;align-items:center}.pay .pay_img[data-v-3c13e60b]{width:48px;height:48px;border-radius:24px;margin-right:12px}.pay .pay_phone[data-v-3c13e60b]{font-size:14px}.pay .pay_change[data-v-3c13e60b]{border:1px solid;padding:0 15px;height:36px;line-height:36px;font-size:14px;border-radius:4px;cursor:pointer;margin-left:15px;flex-shrink:0}.pay .pay_item[data-v-3c13e60b]{height:76px;flex:1;background:#f5f6fa;border-radius:4px;margin-left:16px;padding:16px;box-sizing:border-box}.pay .pay_item .title[data-v-3c13e60b]{font-size:14px}.pay .pay_item .num[data-v-3c13e60b]{font-size:18px;font-weight:800;margin-top:5px}.pay .pay_table[data-v-3c13e60b]{display:flex;padding:0 30px;justify-content:space-between;align-items:center;height:calc(100% - 150px)}.pay .pay_table>div[data-v-3c13e60b]{width:45%}.pay .pay_table .pay_info .pay_left[data-v-3c13e60b]{text-align:left}.pay .pay_discount .pay_text[data-v-3c13e60b]{padding:30px 0 40px;display:flex;justify-content:space-between}.pay .pay_discount_calc[data-v-3c13e60b]{display:flex;flex-direction:column;background-color:#d7d7d7;padding:7px;min-width:350px}.pay .pay_discount_calc .pay_discount_input[data-v-3c13e60b]{position:relative}.pay .pay_discount_calc .pay_discount_input input[data-v-3c13e60b]{height:45px;width:calc(100% - 23px);background-color:#f1f1f1;border-width:0;outline:0;padding-left:20px;margin-bottom:10px;font-size:20px}.pay .pay_discount_calc .pay_discount_input .pay_discount_delete[data-v-3c13e60b]{width:30px;height:auto;position:absolute;right:15px;top:8px;cursor:pointer}.pay .pay_discount_calc .pay_discount_key[data-v-3c13e60b]{display:flex;flex-wrap:wrap;justify-content:space-between}.pay .pay_discount_calc .pay_discount_key>div[data-v-3c13e60b]{width:30%;aspect-ratio:3/2;display:flex;align-items:center;justify-content:center;cursor:pointer}.pay .pay_discount_calc .pay_discount_key>div.active[data-v-3c13e60b]{background-color:#ebeaea}.pay .pay_title[data-v-3c13e60b]{font-size:20px;font-weight:800;color:#333;text-align:center}.pay .pay_total[data-v-3c13e60b]{margin-top:20px;font-size:20px;font-weight:800;color:#333;text-align:center}.pay .pay_name[data-v-3c13e60b]{flex-shrink:0}.pay .pay_content[data-v-3c13e60b]{max-height:220px;max-width:820px;overflow-y:scroll}.pay .pay_content[data-v-3c13e60b]::-webkit-scrollbar{width:0!important}.pay .pay_text[data-v-3c13e60b]{color:#666;font-size:16px;margin-top:20px}.pay .pay_style[data-v-3c13e60b]{font-size:20px}.pay .pay_style .title[data-v-3c13e60b]{padding:20px 0}.pay .pay_type_box[data-v-3c13e60b]{display:flex;justify-content:space-between;flex-wrap:wrap;padding-right:20px;row-gap:15px}.pay .pay_type_box .pay_type_item[data-v-3c13e60b]{width:30%;font-size:18px;aspect-ratio:3/2;border-radius:5px;background-color:#f4f4f4;display:flex;align-items:center;justify-content:center;cursor:pointer}.pay .pay_type_box .pay_type_item.active[data-v-3c13e60b]{background-color:#fff}.pay .pay_card[data-v-3c13e60b]{cursor:pointer}.pay .pay_lable[data-v-3c13e60b]{line-height:32px}.pay .pay_active[data-v-3c13e60b]{border:1px solid}.pay .pay_type[data-v-3c13e60b]{margin:0 10px 0 0}.pay .pay_opt[data-v-3c13e60b]{position:absolute;padding:8px;width:100%;box-sizing:border-box;bottom:0;border-top:1px solid #f2f2f2}.pay .pay_opt .pay_btn[data-v-3c13e60b]{height:48px;width:90px;font-size:14px;border:1px solid;border-radius:4px;margin-right:10px;cursor:pointer}.success[data-v-3c13e60b]{position:relative;height:100%}.success .success_body[data-v-3c13e60b]{padding:16px}.success .success_body .icon[data-v-3c13e60b]{font-size:80px;margin:240px auto 0;display:block}.success .success_body .text[data-v-3c13e60b]{font-size:20px;font-weight:700;text-align:center;margin-top:16px}.success .success_opt[data-v-3c13e60b]{position:absolute;padding:8px;width:100%;box-sizing:border-box;bottom:0;border-top:1px solid #f2f2f2}.success .success_opt .success_btn[data-v-3c13e60b]{height:48px;width:90px;font-size:14px;border:1px solid;border-radius:4px;margin-right:10px;cursor:pointer}",document.head.appendChild(e);import{d as t,q as a,a as o,b as l,o as r,s as i,v as s,E as d,c as n,x as c,e as p,f as u,y as m,t as b,k as f,g as x,h as y,n as v,i as _,F as g,z as h,A as k,B as I,C as w,D,G as S,l as z,j as C,H as V,I as U}from"./index.87a9fe81.js";import{_ as M}from"./order.b10cb578.js";import{t as F,a as A,o as T,b as P,c as L,d as N,p as q,e as j,f as E,m as K,r as R,h as X,i as O,k as Q,l as G,n as Y,q as B,s as W,u as J,v as Z,w as H,x as $,y as ee,z as te,A as ae,B as oe,C as le,D as re}from"./api.bd37d314.js";import{u as ie}from"./userInfo.76997c16.js";import{g as se,d as de}from"./utils.9f6c7cc3.js";var ne="./static/noface.4fb6fd39.png";const ce={class:"content flex_y_center"},pe={class:"inventory flex"},ue={key:0,class:"inventory_alert"},me={class:"inventory_head flex_y_center flex_bt"},be={class:"inventory_title"},fe={class:"table"},xe={class:"flex_y1 body"},ye={key:0},ve={class:"image-slot"},_e={class:"flex_x1"},ge={class:"module_title flex"},he={class:"flex_x1 h1"},ke={class:"module_text h1"},Ie=["onClick"],we={class:"module_unit"},De={class:"flex_y_center"},Se={class:"flex_y_center"},ze={key:1},Ce={class:"image-slot"},Ve={class:"flex_x1"},Ue={class:"module_title flex"},Me={class:"flex_x1 h1"},Fe={class:"module_text h1"},Ae=["onClick"],Te={class:"module_unit"},Pe={class:"flex_y_center"},Le={class:"module_opt flex"},Ne=["onClick"],qe=["onUpdate:modelValue","onFocusin"],je=["onClick"],Ee={key:2,class:"body_null"},Ke={class:"body_title"},Re={class:"order_remarks flex_y_center"},Xe={key:0,class:"remarks_item",style:{display:"flex","align-items":"center"}},Oe={class:"remarks_item",style:{display:"flex","align-items":"center"}},Qe={class:"remarks_item",style:{display:"flex","align-items":"center"}},Ge={key:1,class:"order_remarks flex_y_center"},Ye={key:0,class:"remarks_item"},Be={key:1,class:"remarks_item"},We={key:2,class:"remarks_item"},Je={key:2,class:"order_remarks flex_y_center orderInfoRemark"},Ze={key:0,class:"remarks_item"},He={class:"group flex"},$e={key:0,class:"operate flex flex_x1"},et={class:"operate_table flex"},tt=["onClick"],at={key:0},ot={key:0,class:"operate_body flex_y1 flex"},lt={class:"shop flex_y1 flex"},rt={class:"shop_body flex flex_x1"},it={class:"shop_search flex_y_center"},st={class:"shop_list flex_y1"},dt={class:"flex_wp"},nt=["onClick"],ct={class:"image-slot"},pt={class:"flex_x1"},ut={class:"shop_title h2"},mt={class:"shop_data flex flex_bt"},bt={class:"shop_stock"},ft={key:0,class:"shop_more"},xt={key:0,class:"shop_notice"},yt={class:"shop_table"},vt=["title","onClick"],_t={key:1,class:"operate_body flex_y1 flex_xy_center"},gt={key:0,class:"add_member"},ht={class:"add_member_ipt"},kt={class:"add_member_ipt"},It={class:"add_member_ipt"},wt={class:"add_member_ipt"},Dt={class:"add_member_ipt"},St={class:"add_member_btn_div"},zt={key:1,class:"btn_div"},Ct={key:2,class:"key"},Vt={class:"key_module flex"},Ut={class:"key_content flex flex_wp"},Mt={class:"key_opt"},Ft={key:3,class:"memberDetail"},At={class:"memberDetail_user"},Tt={class:"memberDetail_data"},Pt={class:"memberDetail_name"},Lt={class:"memberDetail_phone"},Nt={class:"memberDetail_module"},qt={class:"memberDetail_item"},jt={class:"num"},Et={class:"memberDetail_item"},Kt={class:"text"},Rt={class:"num"},Xt={class:"memberDetail_item"},Ot={class:"num"},Qt={class:"memberDetail_item"},Gt={class:"num"},Yt={class:"memberDetail_list"},Bt={class:"item"},Wt={class:"text"},Jt={class:"item"},Zt={class:"text"},Ht={class:"item"},$t={class:"text"},ea={class:"item"},ta={class:"text"},aa={class:"memberDetail_order"},oa={key:2,class:"operate_body flex_y1"},la={class:"take"},ra={key:0,class:"take_module"},ia={class:"take_date"},sa={class:"flex flex_wp"},da={class:"take_title h1"},na={class:"take_data flex_bt"},ca={class:"take_num"},pa={class:"take_opt flex_bt flex_y_center"},ua={class:"take_order"},ma={class:"flex"},ba=["onClick"],fa=["onClick"],xa={key:1,class:"take_null"},ya={key:3,class:"operate_body flex_y1 flex_xy_center"},va={class:"key"},_a={class:"key_data"},ga={class:"key_module flex"},ha={class:"key_content flex flex_wp"},ka={class:"key_opt"},Ia={key:1,class:"operate flex_x1"},wa={class:"update"},Da={class:"update_title flex_y_center flex_bt"},Sa={class:"update_body"},za={class:"update_module flex_y_center"},Ca={class:"image-slot"},Va={class:"flex_x1"},Ua={class:"h2"},Ma={class:"update_data flex flex_bt"},Fa={class:"update_stock"},Aa={class:"update_item flex_y_center"},Ta={class:"update_item flex_y_center"},Pa={class:"update_set"},La={class:"update_opt flex flex_x_bottom"},Na={key:2,class:"operate flex_x1"},qa={class:"pay"},ja={class:"pay_body"},Ea={key:0,class:"pay_header"},Ka={class:"pay_data"},Ra={class:"pay_phone"},Xa={class:"pay_phone"},Oa={class:"pay_item"},Qa={class:"pay_item"},Ga={class:"pay_item"},Ya={class:"pay_table"},Ba={class:"pay_info"},Wa={class:"pay_title pay_left"},Ja={class:"pay_text"},Za={class:"pay_text"},Ha={class:"pay_text"},$a={class:"pay_text"},eo={key:0,class:"pay_text"},to={class:"pay_content"},ao={class:"pay_lable"},oo={class:"pay_lable"},lo={key:1,class:"pay_text"},ro={class:"pay_total pay_left"},io={class:"pay_text pay_style"},so={class:"pay_type_box"},no=["onClick"],co={class:"pay_discount"},po={class:"pay_text"},uo={style:{color:"#f00"}},mo={class:"pay_discount_calc"},bo={class:"pay_discount_input"},fo={class:"pay_discount_key"},xo=["onMousedown"],yo={class:"pay_opt flex flex_x_bottom"},vo={key:3,class:"operate flex_x1"},_o={class:"success"},go={class:"success_body"},ho={class:"success_opt flex flex_x_bottom"},ko={class:"dialog-footer"},Io={class:"dialog-footer"},wo={class:"dialog-footer"},Do={class:"store_val_pay"},So={class:"store_val_pay"},zo={class:"pay_type_list"},Co=["onClick"],Vo={class:"dialog-footer"},Uo={class:"order_info"},Mo={class:"order_info_item"},Fo={class:"order_info_item"},Ao={class:"order_info_item"},To={class:"order_info_item"},Po={class:"order_info_item"},Lo={class:"order_info_item"},No={class:"dialog-footer"},qo={class:"table_list"},jo=["onClick"],Eo={style:{color:"black","font-weight":"bold","margin-bottom":"5px"}},Ko={class:"dialog-footer"};var Ro=t({__name:"index",setup(e){const t=a().query.tid,Ro=o({table:{list:[],free:[],select:t?Number(t):"",info:{},order:{},order_goods:[],sysset:{},cartBuyview:{},productData:[]},operateTable:[{lable:"商品",value:0},{lable:"会员",value:1},{lable:"取单",value:2},{lable:"直接收款",value:3}],operateIndex:0,shopTable:[{name:"全部",id:""}],shopIndex:0,shopList:[],shopKeyWord:{page:1,limit:30,name:"",cid:"",code:"",apifrom:"vue"},shopMoreType:!1,shopUpdate:"",dialogState:!1,remarkState:!1,memberState:"1",memberUserInfo:"",keyIndex:"",dataInfo:{total:0,totalprice:0,prolist:[]},dataInfoHang:[],shopFocus:"0",memberData:"",memberFocus:"0",directData:"",directFocus:"0",updateState:"0",remove_zero:0,couponInfo:[],couponInfoState:!1,storeValState:!1,orderInfoState:!1,tableListState:!1,storeVal:{value:0,paytype:""},payInfo:{couponlist:[],memberinfo:{}},payData:{couponid:"",paytype:"",userscore:""},orderInfo:{},orderList:{},orderDetail:{},payTypeListMember:[{lable:"现金",value:"3"},{lable:"余额",value:"4"},{lable:"本店储值",value:"a"},{lable:"微信",value:"1"},{lable:"支付宝",value:"2"},{lable:"QR",value:"b"}],payTypeListStoreVal:[{lable:"现金",value:"3"},{lable:"微信",value:"1"},{lable:"支付宝",value:"2"},{lable:"QR",value:"b"}],payTypeListNormal:[{lable:"微信",value:"1"},{lable:"现金",value:"3"}],scanTime:"",scanCodeGoods:"",scanCode:"",scanState:!0,printId:"",proState:!1,directState:!1,numberFocus:"0",noticeState:!1,updateFocus:"0",numberIndex:""}),Xo=o({realname:"",tel:"",birthday:"",sex:1,stored_val:0}),Oo=o(["1","2","3","4","5","6","7","8","9",".","0","OK"]),Qo=se(),Go=o({discount:"",discountVal:0,keyIndex:""}),Yo=e=>{ul(Ro.memberUserInfo.id,e)},Bo=ie(),Wo=l();r((()=>{if(Wo.currentRoute.value.query.id){let e=Wo.currentRoute.value.query.id;sessionStorage.setItem("cashierId",e)}t&&Ho(t),zl(),Wl()})),i((()=>{document.onkeydown=null}));const Jo=e=>{Wo.replace(e)},Zo=e=>{N(e,Ro.table.info.bid).then((e=>{if(1==e.status){let t=[];for(let a of e.data)t.push(...a.prolist);if(Ro.table.productData=[{name:"全部",id:"",prolist:t}],Ro.table.productData.push(...e.data),ql(0,"",t),Ro.table.sysset=e.sysset,Ro.dataInfo.prolist=e.cartList.list.reverse(),Ro.dataInfo.total=e.cartList.total,Ro.dataInfo.totalprice=e.cartList.totalprice,e.cartList.total){let t=e.cartList.list.map((e=>`${e.proid},${e.ggid},${e.num}`)).join("-");xl(Ro.table.select,t)}}}))},Ho=e=>{F(e).then((t=>{if(t){let{info:a,order:o,order_goods:l}=t;Object.assign(Ro.table,{info:a,order:o,order_goods:l}),o.originalTotalPrice=(Number(o.totalprice)+Number(o.tea_fee)+Number(o.server_fee)+Number(o.taxes)).toFixed(2),o.renshu=o.renshu||1,o.prodata=l.map((e=>`${e.proid},${e.ggid},${e.num}`)).join("-"),o.discountVal=0,Zo(e)}}))},$o=s((()=>(Number(Ro.table.order.originalTotalPrice)*Number(Go.discount||10)/10-Go.discountVal).toFixed(2))),el=s((()=>Go.discount?(Ro.table.order.originalTotalPrice*(10-Number(Go.discount))/10).toFixed(2):0)),tl=()=>{Ro.table.select&&(Jo(`/index/index?tid=${Ro.table.select}`),Ho(Ro.table.select))};s((()=>{let e=parseFloat(Ro.table.sysset.tea_fee)*Ro.table.order.renshu,t=parseFloat(Ro.table.sysset.server_fee)*Ro.table.order.renshu,a=Number(Ro.table.sysset.taxes),o=Number(Ro.table.order.totalprice)+e+t+a;return o<0&&(o=0),o})),s((()=>{let e=0;for(let t of Ro.dataInfo.prolist)e+=t.guige.sell_price*t.num;return e}));const al=()=>{Ro.proState=!0},ol=()=>{T();let e={apifrom:"vue",orderid:Ro.printId};q(e).then((e=>{L(),"1"==e.status?d.success("已打印"):d.error(e.msg)}))},ll=()=>{Ro.updateState="0",Ro.operateIndex="0",Ro.memberState="1",Ro.memberUserInfo="",Ro.payData.paytype=""},rl=()=>{if(!Ro.payData.paytype)return void d.error("请选择支付方式");if("4"==Ro.payData.paytype&&Number($o.value)>Number(Ro.memberUserInfo.money))return void d.error("余额不足");if("a"==Ro.payData.paytype&&Number($o.value)>Number(Ro.memberUserInfo.stored_money))return void d.error("本店储值不足");let e=Ro.payTypeListMember.find((e=>e.value==Ro.payData.paytype)).lable,t={info:{mid:Ro.memberUserInfo.id,usescore:Ro.payData.userscore,rebate:Go.discount,discount:Go.discountVal,tableId:Ro.table.select,paytype:e}};T(),j(t).then((e=>{L(),1==e.status?(T(),d.success(e.msg),E(Ro.table.select).then((e=>{L(),tl(),Ro.operateIndex=0,Ro.directState=!1,Ro.memberState="1",Ro.updateState="0",Ro.payData.paytype=""}))):d.error(e.msg)}))},il=()=>{T();let e={apifrom:"vue",page:1,limit:100,mid:Ro.memberUserInfo.id};K(e).then((e=>{L(),"1"==e.status?(Ro.couponInfo=e.data,Ro.couponInfoState=!0):d.error(e.msg)}))},sl=()=>{0!=Ro.dataInfo.prolist.length?(T(),E(Ro.table.select).then((e=>{L(),Zo(Ro.table.select)}))):d.error("购物车为空")},dl=()=>{Ro.updateState="0",Ro.payData.paytype=""},nl=()=>{Ro.updateState="0",Ro.memberState="1",Ro.memberUserInfo=""},cl=()=>{Ro.memberState="3"};const pl=()=>{var e;(e=Xo.tel,/^1[3-9]\d{9}$/.test(e))?(T(),R(Xo).then((e=>{L(),"1"==e.status?(d.success("注册成功"),Ro.memberState="1",Ro.memberData=""):d.error(e.msg)}))):d.error("请输入正确的手机号")},ul=(e,t)=>{X({uid:e,page:t,limit:5}).then((e=>{"1"==e.status&&(Ro.orderList=e.data)}))},ml=()=>{let e=Ro.payTypeListStoreVal.find((e=>e.value==Ro.storeVal.paytype)).lable,t={stored_val:Ro.storeVal.value,uid:Ro.memberUserInfo.id,paytype:e,pm:"1"};T(),H(t).then((e=>{if(L(),"1"==e.status){Ro.storeVal.value="1";let t={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),keyword:Ro.memberUserInfo.id};$(t).then((e=>{"1"==e.status&&(Ro.memberUserInfo=e.data,ul(e.data.id,1))})),d.success(e.msg),Ro.storeValState=!1}else d.error(e.msg)}))},bl=()=>{T(),Ro.orderList={};let e={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),keyword:Ro.memberData};$(e).then((e=>{L(),"1"==e.status?(Ro.memberUserInfo=e.data,Ro.memberData="",Ro.memberState="2",ul(e.data.id,1)):d.error(e.msg)}))},fl=e=>{if(3==Ro.table.order.status||3==Ro.table.info.status)return void d.error("请先完成清台");if(""==Ro.table.select)return void d.error("请选择桌台号");if(Ro.dataInfo.prolist.length<=0)return void d.error("请先选择商品");T();let t=[];for(let e of Ro.dataInfo.prolist)t.push(`${e.proid},${e.ggid},${e.num}`);let a=[{bid:Ro.table.info.bid,prodata:t.join("-"),renshu:Ro.table.order.renshu,message:Ro.dataInfo.remark}],o={frompage:"admin",tableid:Ro.table.select,usescore:0,buydata:a};Ro.table.order.id&&3!=Ro.table.order.status?O(o).then((e=>{L(),"1"==e.status?(d.success("下单成功"),tl()):d.error(e.msg)})):Q(o).then((e=>{L(),"1"==e.status?(d.success("下单成功"),Ro.remarkState=!1,Ro.dataInfo.remark="",E(Ro.table.select).then((()=>{tl()}))):d.error(e.msg)}))},xl=(e,t)=>{G(e,t).then((e=>{if(1==e.status)for(let t in e.allbuydata)Ro.table.cartBuyview=e.allbuydata[t];else d.error(e.msg)}))},yl=()=>{""!=Ro.table.select?Ro.table.order.id&&"3"!=Ro.table.order.status?""==Ro.memberUserInfo?(Ro.noticeState=!0,z.confirm("订单未绑定会员，是否需要绑定会员进行结算?","提示",{confirmButtonText:"会员登录(Enter)",cancelButtonText:"跳过(Esc)",distinguishCancelAndClose:!0,type:"warning",callback:function(e){Ro.noticeState=!1,"confirm"==e?(Ro.operateIndex=1,Ro.directState=!1,Ro.scanCode="",Ro.updateState="0",Ro.memberState="1",Ho(Ro.table.select),xl(Ro.table.select,Ro.table.order.prodata)):"cancel"==e&&(Ro.scanCode="",Ro.operateIndex=0,Ro.updateState="3",Ro.directState=!1,Ro.memberState="1",Ho(Ro.table.select))}})):(Ro.updateState="2",xl(Ro.table.select,Ro.table.order.prodata)):d.error("请先下单"):d.error("请选择桌台号")},vl=()=>{""!=Ro.table.select?zl((e=>{for(let t of e)0==t.status&&Ro.table.free.push(t);Ro.table.free.length>0?Ro.tableListState=!0:d.error("没有空闲桌台")})):d.error("请选择当前桌台号")},_l=()=>{if(""==Ro.table.select)return void d.error("请选择桌台号");if(console.log(Ro.table.order),0==Ro.table.info.status)return void d.error("桌台已空闲");let e=Ro.table.select;3!=Ro.table.info.status?3==Ro.table.order.status?B(e).then((t=>{1==t.status?(d.success("开始清台"),E(e).then((e=>{tl()}))):d.error(t.msg)})):d.error("请先结算当前桌台订单"):Y(e).then((t=>{1==t.status?(d.success("清台成功"),E(e).then((e=>{tl()}))):d.error(t.msg)}))},gl=e=>{z.confirm("删除该单?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{T();let a={apifrom:"vue",orderid:e,o_id:Ro.table.order.id};te(a).then((e=>{L(),"1"==e.status?t&&Ho(t):d.error(e.msg)}))})).catch((()=>{d({type:"info",message:"已取消"})}))},hl=()=>{let e={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),status:2};re(e).then((e=>{"1"==e.status?Ro.dataInfoHang=e.data:d.error(e.msg)}))},kl=()=>{if(Ro.shopUpdate.upPrice){T();let e={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),id:Ro.shopUpdate.id,price:Ro.shopUpdate.upPrice};W(e).then((e=>{L(),"1"==e.status?(d({message:"操作成功",type:"success"}),Sl()):d.error(e.msg)}))}else d.error("请输入定价")},Il=(e,t,a)=>{let o=Ro.table.select;if(""==o)return void d.error("请选择桌台号");let l={tableId:o,ggid:e,num:a,proid:t};T(),oe(l).then((e=>{L(),1==e.status?Zo(o):d.error(e.msg)}))},wl=()=>{T();let e={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),proid:"-99",ggid:"",num:1,price:Ro.directData};P(e).then((e=>{L(),"1"==e.status?(Ro.directState=!0,Sl()):d.error(e.msg)}))},Dl=()=>{if(Ro.specsInfo.ggid){T();let e={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),proid:Ro.specsInfo.id,ggid:Ro.specsInfo.ggid,num:1,price:""};P(e).then((e=>{L(),"1"==e.status?(Ro.dialogState=!1,Ro.specsInfo.ggid=null,Sl()):d.error(e.msg)}))}else d.error("请选择商品规格")},Sl=()=>{let e={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),remove_zero:Ro.remove_zero};J(e).then((e=>{L(),"1"==e.status?e.data?Ro.dataInfo=e.data:Ro.dataInfo={prolist:[]}:d.error(e.msg)}))},zl=e=>{Ro.table.free=[],A().then((t=>{1==t.status&&(Ro.table.list=t.datalist,e&&e(t.datalist))}))},Cl=()=>{Ro.shopMoreType||++Ro.shopKeyWord.page},Vl=(e,t)=>{Ro.updateState=e,t&&(e=>{Ro.shopUpdate=e})(t)},Ul=()=>{Ro.shopFocus="1"},Ml=()=>{Ro.shopFocus="0"},Fl=()=>{Ro.memberFocus="1"},Al=()=>{Ro.memberFocus="0"},Tl=()=>{Ro.directFocus="1"},Pl=()=>{Ro.directFocus="0"},Ll=()=>{Ro.updateFocus="1"},Nl=()=>{Ro.updateFocus="0"},ql=(e,t,a)=>{Ro.shopIndex=e,Ro.shopKeyWord.cid=t,Ro.shopKeyWord.page=1,Ro.shopMoreType=!1,Ro.shopList=a},jl=e=>{if(1==Ro.operateIndex)Ro.keyIndex=e,"0"==Ro.memberFocus&&("clear"==e?Ro.memberData="":"delete"==e?Ro.memberData=Ro.memberData.substr(0,Ro.memberData.length-1):"confirm"==e?1==Ro.memberState&&(""==Ro.memberData?d.error("请输入会员手机号或会员码"):bl()):Ro.memberData=Ro.memberData+e),"1"==Ro.memberFocus&&"confirm"==e&&(""==Ro.memberData?d.error("请输入会员手机号或会员码"):bl());else if(3==Ro.operateIndex){if(Ro.keyIndex=e,Ro.directState&&"confirm"==e)return void yl();"0"==Ro.directFocus&&("clear"==e?Ro.directData="":"delete"==e?Ro.directData=Ro.directData.substr(0,Ro.directData.length-1):"confirm"==e?""==Ro.directData?d.error("请输入收款金额"):wl():Ro.directData=Ro.directData+e),"1"==Ro.directFocus&&"confirm"==e&&(""==Ro.directData?d.error("请输入收款金额"):wl())}},El=()=>{Go.discount&&(Go.discount=Go.discount.substring(0,Go.discount.length-1))},Kl=()=>{Go.keyIndex=""},Rl=e=>{Go.keyIndex="";const t=()=>{let e=Go.discount.split("");e.pop(),Go.discount=e.join("")};("."===e.key&&(e=>{let t=0;for(let a=0;a<e.length;a++)"."===e[a]&&t++;return t})(Go.discount)>1||Go.discount.length>4||Number(Go.discount)>=10)&&t()},Xl=e=>{let t=e.key;Go.keyIndex=t};let Ol;const Ql=e=>{if(Go.keyIndex=e,Ol&&clearTimeout(Ol),Ol=setTimeout((()=>{Kl()}),100),"OK"===e)return void Go.discount;if("."===e&&Go.discount.includes("."))return;let t=Go.discount+""+e;t.length>4||Number(t)>=10||(Go.discount=t)},Gl=()=>{Ro.keyIndex=""};var Yl;const Bl=e=>{jl(e),clearTimeout(Yl),Yl=setTimeout((()=>{Gl()}),100)},Wl=()=>{document.onkeydown=function(e){if("2"==Ro.updateState){if("0123456789.".includes(e.key))return void Ql(e.key);"Backspace"==e.key&&El()}if(!Ro.noticeState&&"1"!=Ro.numberFocus&&"Escape"!=e.key)if(0==Ro.operateIndex)if("Enter"==e.key&&Ro.scanState){if("1"==Ro.shopFocus){if("Enter"==e.key){Ro.shopKeyWord.page=1,Ro.shopMoreType=!1,Ro.shopList=[];let e=Ro.table.productData[0].prolist.filter((e=>e.name.includes(Ro.shopKeyWord.name)));console.log(Ro.shopKeyWord.name),ql(0,"",e)}}else if(Ro.dataInfo.prolist.length&&("1"==Ro.updateState?kl():"3"==Ro.updateState?rl():""==Ro.memberUserInfo?yl():Ro.operateIndex=1),"4"==Ro.updateState)return Ro.updateState="0",Ro.operateIndex="0",Ro.memberState="1",Ro.memberUserInfo="",void(Ro.payData.paytype="")}else if("0"==Ro.updateState){if("Enter"==e.key){T();let e={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),barcode:Ro.scanCodeGoods};P(e).then((e=>{L(),Ro.scanCodeGoods="","1"==e.status?Sl():d.error(e.msg)}))}else Ro.scanCodeGoods=Ro.scanCodeGoods+e.key;Ro.scanState=!1,clearTimeout(Ro.scanTime),Ro.scanTime=setTimeout((()=>{Ro.scanState=!0}),100)}else"3"==Ro.updateState&&("Enter"==e.key?(T(),rl()):Ro.scanCode=Ro.scanCode+e.key,Ro.scanState=!1,clearTimeout(Ro.scanTime),Ro.scanTime=setTimeout((()=>{Ro.scanState=!0}),100));else if(1==Ro.operateIndex)if(""==Ro.memberUserInfo)"0"==e.key?Bl(0):"1"==e.key?Bl(1):"2"==e.key?Bl(2):"3"==e.key?Bl(3):"4"==e.key?Bl(4):"5"==e.key?Bl(5):"6"==e.key?Bl(6):"7"==e.key?Bl(7):"8"==e.key?Bl(8):"9"==e.key?Bl(9):"Backspace"==e.key?Bl("delete"):"Enter"==e.key&&Bl("confirm");else if("Enter"==e.key&&Ro.scanState){if(Ro.dataInfo.prolist.length&&("0"==Ro.updateState?Ro.updateState="2":"1"==Ro.updateState?kl():"2"==Ro.updateState&&rl()),"4"==Ro.updateState)return Ro.updateState="0",Ro.operateIndex="0",Ro.memberState="1",Ro.memberUserInfo="",void(Ro.payData.paytype="")}else"2"==Ro.updateState&&("Enter"==e.key?rl():Ro.scanCode=Ro.scanCode+e.key,Ro.scanState=!1,clearTimeout(Ro.scanTime),Ro.scanTime=setTimeout((()=>{Ro.scanState=!0}),100));else 3==Ro.operateIndex&&("0"==e.key?Bl(0):"1"==e.key?Bl(1):"2"==e.key?Bl(2):"3"==e.key?Bl(3):"4"==e.key?Bl(4):"5"==e.key?Bl(5):"6"==e.key?Bl(6):"7"==e.key?Bl(7):"8"==e.key?Bl(8):"9"==e.key?Bl(9):"."==e.key?Bl("."):"Backspace"==e.key?Bl("delete"):"Enter"==e.key&&Bl("confirm"))}};return(e,t)=>{const a=n("el-option"),o=n("el-select"),l=n("el-icon"),r=n("el-image"),i=n("el-input-number"),s=n("el-input"),F=n("Search"),A=n("el-date-picker"),P=n("el-radio"),N=n("el-radio-group"),q=n("CircleClose"),j=n("el-table-column"),E=n("el-button"),K=n("el-table"),R=n("el-pagination"),X=n("Close"),O=n("el-card"),Q=n("el-space"),G=n("el-checkbox"),Y=n("CircleCheckFilled"),B=n("el-form-item"),W=n("el-form"),J=n("el-dialog"),H=c("infinite-scroll");return C(),p("div",ce,[u("div",pe,["2"==Ro.updateState||"3"==Ro.updateState?(C(),p("div",ue)):m("",!0),u("div",me,[u("span",be,b(e.$t("left.settlement_list"))+"（ "+b(Ro.dataInfo.total)+" "+b(e.$t("left.piect"))+" ）",1),u("span",fe,[f(b(e.$t("left.table_number"))+" ",1),x(o,{modelValue:Ro.table.select,"onUpdate:modelValue":t[0]||(t[0]=e=>Ro.table.select=e),placeholder:e.$t("left.select_table"),style:{width:"150px"},onChange:tl},{default:y((()=>[(C(!0),p(g,null,h(Ro.table.list,(e=>(C(),V(a,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])]),u("span",null,[f(b(e.$t("left.number_of_people"))+"：",1),u("span",{style:v("color:"+_(Bo).userInfo.color1)},b(Ro.table.order.renshu||e.$t("left.not_have")),5)])]),u("div",xe,[Ro.table.order_goods&&3!=Ro.table.order.status?(C(),p("div",ye,[(C(!0),p(g,null,h(Ro.table.order_goods,((e,t)=>(C(),p("div",{class:"module flex_y_center",key:t,style:{"background-color":"#ffe"}},[x(r,{class:"module_image",src:e.pic},{error:y((()=>[u("div",ve,[x(l,null,{default:y((()=>[x(_(U))])),_:1})])])),_:2},1032,["src"]),u("div",_e,[u("div",ge,[u("div",he,[u("div",ke,b(e.name),1)]),u("div",{onClick:t=>gl(e.id),class:"module_up flex_r"}," 删除 ",8,Ie)]),u("div",we,"规格："+b(e.ggname),1),u("div",De,[u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"module_price flex_x1"},b(_(Qo)+e.sell_price),5),u("div",Se,b(e.num),1),u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"module_total flex_x1"},b(_(Qo)+e.totalprice),5)])])])))),128))])):m("",!0),Ro.dataInfo.prolist?(C(),p("div",ze,[(C(!0),p(g,null,h(Ro.dataInfo.prolist,((e,a)=>(C(),p("div",{class:"module flex_y_center",key:e.id},[x(r,{class:"module_image",src:e.product.pic},{error:y((()=>[u("div",Ce,[x(l,null,{default:y((()=>[x(_(U))])),_:1})])])),_:2},1032,["src"]),u("div",Ve,[u("div",Ue,[u("div",Me,[u("div",Fe,b(e.product.name),1)]),u("div",{onClick:t=>(e=>{let t={id:e};T(),le(t).then((e=>{L(),1==e.status?Zo(Ro.table.select):d.error(e.msg)}))})(e.id),class:"module_up flex_r"}," 删除 ",8,Ae)]),u("div",Te,"规格："+b(e.guige.name),1),u("div",Pe,[u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"module_price flex_x1"},b(_(Qo)+e.guige.sell_price),5),u("div",Le,[u("div",{class:"module_icon flex_xy_center",onClick:t=>((e,t)=>{1!=t&&Il(e.ggid,e.proid,-1)})(e,e.num)}," - ",8,Ne),I(u("input",{type:"text","onUpdate:modelValue":t=>e.num=t,onFocusin:e=>(e=>{Ro.numberFocus="1",Ro.numberIndex=e})(a),onFocusout:t[1]||(t[1]=e=>(Ro.numberFocus="0",void(Ro.numberIndex=""))),onChange:al,style:v(1==Ro.numberFocus&&Ro.numberIndex==a?"outline: 1px solid"+_(Bo).userInfo.color1:""),class:"module_input flex_x1"},null,44,qe),[[w,e.num]]),u("div",{class:"module_icon flex_xy_center",onClick:t=>Il(e.ggid,e.proid,1)}," + ",8,je)]),u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"module_total flex_x1"},b(_(Qo)+e.guige.sell_price*e.num),5)])])])))),128))])):m("",!0),Ro.dataInfo.prolist.length?m("",!0):(C(),p("div",Ee,[t[64]||(t[64]=u("img",{src:"data:image/png;base64,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",class:"body_icon",alt:""},null,-1)),u("div",Ke,b(e.$t("left.no_goods")),1)]))]),u("div",Re,[Ro.table.order.id?(C(),p("div",Xe,[t[65]||(t[65]=u("label",null,"订单菜品：",-1)),u("span",{style:v("color:"+_(Bo).userInfo.color1+";font-size:20px")},b(_(Qo)+(3!=Ro.table.order.status?Ro.table.order.totalprice:"0.00")),5)])):m("",!0),u("div",Oe,[u("label",null,b(e.$t("left.add_goods"))+"：",1),u("span",{style:v("color:"+_(Bo).userInfo.color1+";font-size:20px")},b(_(Qo)+Ro.dataInfo.totalprice),5)]),u("div",Qe,[u("label",null,b(e.$t("left.Total_dishes"))+"：",1),u("span",{style:v("color:"+_(Bo).userInfo.color1+";font-size:20px")},b(_(Qo)+(Number((3!=Ro.table.order.status?Ro.table.order.totalprice:0)||0)+Number(Ro.dataInfo.totalprice||0)).toFixed(2)),5)])]),!Ro.table.order.id&&Ro.table.order.renshu?(C(),p("div",Ge,[Ro.table.order.id?m("",!0):(C(),p("div",Ye,[t[66]||(t[66]=u("label",null,"就餐人数：",-1)),u("span",null,[x(i,{modelValue:Ro.table.order.renshu,"onUpdate:modelValue":t[2]||(t[2]=e=>Ro.table.order.renshu=e),min:1,max:100,style:{width:"120px"}},null,8,["modelValue"])])])),Ro.table.cartBuyview.server_fee?(C(),p("div",Be,[t[67]||(t[67]=u("label",null,"服务费：",-1)),u("span",null,b(Ro.table.cartBuyview.server_fee*Ro.table.order.renshu),1)])):m("",!0),Ro.table.sysset.tea_fee&&Ro.table.order.renshu?(C(),p("div",We,[t[68]||(t[68]=u("label",null,"茶位费：",-1)),u("span",null,b(Ro.table.sysset.tea_fee*Ro.table.order.renshu),1)])):m("",!0)])):m("",!0),Ro.table.order.id?m("",!0):(C(),p("div",Je,[Ro.table.order.id?m("",!0):(C(),p("div",Ze,[u("label",null,b(e.$t("left.remark"))+"：",1),u("span",null,[x(s,{modelValue:Ro.dataInfo.remark,"onUpdate:modelValue":t[3]||(t[3]=e=>Ro.dataInfo.remark=e),style:{width:"100%"},placeholder:e.$t("left.re_remark")},null,8,["modelValue","placeholder"])])]))])),u("div",He,[3!=Ro.table.order.status&&3!=Ro.table.info.status?(C(),p("div",{key:0,onClick:sl,style:"color:#fff",class:"group_btn flex_xy_center bg-red border-red"},b(e.$t("left.clear")),1)):(C(),p("div",{key:1,onClick:_l,class:k([3==Ro.table.info.status?"bg-green border-green":"bg-red border-red","group_btn flex_xy_center"]),style:"color:#fff"},b(3==Ro.table.info.status?"清台完成":"清台"),3)),u("div",{onClick:fl,style:v("color:#fff;border:1px solid"+_(Bo).userInfo.color1+";background:"+_(Bo).userInfo.color1),class:"group_btn flex_xy_center"},b(Ro.table.order.id&&3!=Ro.table.order.status?"加菜":e.$t("left.submit")),5),u("div",{onClick:yl,style:v("color:#fff;border:1px solid"+_(Bo).userInfo.color1+";background:"+_(Bo).userInfo.color1),class:"group_btn flex_xy_center"},b(e.$t("left.gathering")),5),u("div",{onClick:vl,style:v("color:#fff;border:1px solid"+_(Bo).userInfo.color1+";background:"+_(Bo).userInfo.color1),class:"group_btn flex_xy_center"},b(e.$t("left.change_table")),5)])]),"0"==Ro.updateState?(C(),p("div",$e,[u("div",et,[(C(!0),p(g,null,h(Ro.operateTable,((e,t)=>(C(),p("div",{key:t,class:"operate_item",style:v(Ro.operateIndex==t?"color:"+_(Bo).userInfo.color1+";border-bottom:2px solid"+_(Bo).userInfo.color1:""),onClick:t=>{return a=e.value,void(Ro.operateIndex=a);var a}},[Ro.dataInfoHang.length?(C(),p("div",at,[2==t?(C(),p("div",{key:0,style:v("background:"+_(Bo).userInfo.color1),class:"operate_num"},b(Ro.dataInfoHang.length),5)):m("",!0)])):m("",!0),f(" "+b(e.lable),1)],12,tt)))),128))]),0==Ro.operateIndex?(C(),p("div",ot,[u("div",lt,[u("div",rt,[u("div",it,[x(l,{class:"shop_icon",color:"#808695",size:20},{default:y((()=>[x(F)])),_:1}),I(u("input",{onFocusin:Ul,onFocusout:Ml,type:"text","onUpdate:modelValue":t[4]||(t[4]=e=>Ro.shopKeyWord.name=e),class:"shop_input flex_x1",style:v(1==Ro.shopFocus?"outline: 1px solid"+_(Bo).userInfo.color1:""),placeholder:"输入商品条形码或商品名称"},null,36),[[w,Ro.shopKeyWord.name]])]),I((C(),p("div",st,[u("div",dt,[(C(!0),p(g,null,h(Ro.shopList,((e,t)=>(C(),p("div",{key:t,onClick:t=>Il(e.gglist[0].id,e.id,1),class:"shop_module flex flex_y_center"},[x(r,{class:"shop_img",src:e.pic},{error:y((()=>[u("div",ct,[x(l,null,{default:y((()=>[x(_(U))])),_:1})])])),_:2},1032,["src"]),u("div",pt,[u("div",ut,b(e.name),1),u("div",mt,[u("span",{style:v("color:"+_(Bo).userInfo.color1)},b(_(Qo)+e.sell_price),5),u("span",bt,"库存:"+b(e.stock),1)])])],8,nt)))),128))]),Ro.shopMoreType?(C(),p("div",ft,"暂无更多")):m("",!0)])),[[D,Ro.shopList.length],[H,Cl]]),Ro.shopList.length?m("",!0):(C(),p("div",xt,t[69]||(t[69]=[u("img",{src:"./static/scan.108123cd.png",alt:""},null,-1),u("div",{class:"text"},"可使用扫码枪或输入商品名称/条码查找商品",-1)])))]),u("div",yt,[(C(!0),p(g,null,h(Ro.table.productData,((e,t)=>(C(),p("div",{key:t,title:e.name,class:"shop_item h1",style:v(Ro.shopIndex==t?"color:"+_(Bo).userInfo.color1+";background:rgba("+_(Bo).userInfo.color1rgb.red+","+_(Bo).userInfo.color1rgb.green+","+_(Bo).userInfo.color1rgb.blue+",0.2)":""),onClick:a=>ql(t,e.id,e.prolist)},b(e.name),13,vt)))),128))])])])):m("",!0),1==Ro.operateIndex?(C(),p("div",_t,["3"==Ro.memberState?(C(),p("div",gt,[t[77]||(t[77]=u("div",{class:"add_member_title"},"添加新会员",-1)),u("div",ht,[t[70]||(t[70]=u("label",null,"姓名",-1)),I(u("input",{type:"text","onUpdate:modelValue":t[5]||(t[5]=e=>Xo.realname=e),placeholder:"请输入姓名",maxLength:20},null,512),[[w,Xo.realname]])]),u("div",kt,[t[71]||(t[71]=u("label",null,"手机号*",-1)),I(u("input",{type:"number",class:"mobile","onUpdate:modelValue":t[6]||(t[6]=e=>Xo.tel=e),placeholder:"请输入手机号"},null,512),[[w,Xo.tel]])]),u("div",It,[t[72]||(t[72]=u("label",null,"生日",-1)),x(A,{modelValue:Xo.birthday,"onUpdate:modelValue":t[7]||(t[7]=e=>Xo.birthday=e),type:"date",placeholder:"请输入生日"},null,8,["modelValue"])]),u("div",wt,[t[75]||(t[75]=u("label",null,"性别",-1)),x(N,{modelValue:Xo.sex,"onUpdate:modelValue":t[8]||(t[8]=e=>Xo.sex=e),class:"ml-4"},{default:y((()=>[x(P,{value:1,size:"large"},{default:y((()=>t[73]||(t[73]=[f("男")]))),_:1}),x(P,{value:2,size:"large"},{default:y((()=>t[74]||(t[74]=[f("女")]))),_:1})])),_:1},8,["modelValue"])]),u("div",Dt,[t[76]||(t[76]=u("label",null,"储值",-1)),I(u("input",{type:"number","onUpdate:modelValue":t[9]||(t[9]=e=>Xo.stored_val=e),placeholder:"请输入储值",maxlength:"8"},null,512),[[w,Xo.stored_val]])]),u("div",St,[u("div",{onClick:t[10]||(t[10]=e=>Ro.memberState=1),style:v("color:"+_(Bo).userInfo.color1+";border: 1px solid "+_(Bo).userInfo.color1),class:"add_member_btn flex_xy_center"}," 取消 ",4),u("div",{onClick:pl,style:v("color:#fff;background:"+_(Bo).userInfo.color1),class:"add_member_btn flex_xy_center"}," 确定 ",4)])])):m("",!0),"1"==Ro.memberState?(C(),p("div",zt,[u("div",{onClick:cl,style:v("color:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),class:"memberDetail_btn"}," 添加新会员 ",4)])):m("",!0),"1"==Ro.memberState?(C(),p("div",Ct,[t[80]||(t[80]=u("div",{class:"key_title"}," 请输入会员手机号或会员码查询会员 ",-1)),I(u("input",{onFocusin:Fl,onFocusout:Al,type:"text",class:"key_input",style:v(1==Ro.memberFocus?"outline: 1px solid"+_(Bo).userInfo.color1:""),"onUpdate:modelValue":t[11]||(t[11]=e=>Ro.memberData=e),placeholder:"输入会员手机号/会员码"},null,36),[[w,Ro.memberData]]),u("div",Vt,[u("div",Ut,[u("div",{onMouseup:Gl,onMousedown:t[12]||(t[12]=e=>jl(7)),class:k(["key_item","7"==Ro.keyIndex?"item_active":""])}," 7 ",34),u("div",{onMouseup:Gl,onMousedown:t[13]||(t[13]=e=>jl(8)),class:k(["key_item","8"==Ro.keyIndex?"item_active":""])}," 8 ",34),u("div",{onMouseup:Gl,onMousedown:t[14]||(t[14]=e=>jl(9)),class:k(["key_item","9"==Ro.keyIndex?"item_active":""])}," 9 ",34),u("div",{onMouseup:Gl,onMousedown:t[15]||(t[15]=e=>jl(4)),class:k(["key_item","4"==Ro.keyIndex?"item_active":""])}," 4 ",34),u("div",{onMouseup:Gl,onMousedown:t[16]||(t[16]=e=>jl(5)),class:k(["key_item","5"==Ro.keyIndex?"item_active":""])}," 5 ",34),u("div",{onMouseup:Gl,onMousedown:t[17]||(t[17]=e=>jl(6)),class:k(["key_item","6"==Ro.keyIndex?"item_active":""])}," 6 ",34),u("div",{onMouseup:Gl,onMousedown:t[18]||(t[18]=e=>jl(1)),class:k(["key_item","1"==Ro.keyIndex?"item_active":""])}," 1 ",34),u("div",{onMouseup:Gl,onMousedown:t[19]||(t[19]=e=>jl(2)),class:k(["key_item","2"==Ro.keyIndex?"item_active":""])}," 2 ",34),u("div",{onMouseup:Gl,onMousedown:t[20]||(t[20]=e=>jl(3)),class:k(["key_item","3"==Ro.keyIndex?"item_active":""])}," 3 ",34),t[78]||(t[78]=u("div",{class:"key_item"},null,-1)),u("div",{onMouseup:Gl,onMousedown:t[21]||(t[21]=e=>jl(0)),class:k(["key_item","0"==Ro.keyIndex?"item_active":""])}," 0 ",34),t[79]||(t[79]=u("div",{class:"key_item"},null,-1))]),u("div",Mt,[u("div",{onMouseup:Gl,onMousedown:t[22]||(t[22]=e=>jl("clear")),class:k(["key_item key_row","clear"==Ro.keyIndex?"item_active":""])}," 清除 ",34),u("div",{onMouseup:Gl,onMousedown:t[23]||(t[23]=e=>jl("delete")),class:k(["key_item key_row","delete"==Ro.keyIndex?"item_active":""])},[x(l,null,{default:y((()=>[x(q)])),_:1})],34),u("div",{onMouseup:Gl,onMousedown:t[24]||(t[24]=e=>jl("confirm")),class:"key_confirm",style:v("confirm"==Ro.keyIndex?"border-color:"+_(Bo).userInfo.color1+";background:"+_(Bo).userInfo.color1:"border-color:"+_(Bo).userInfo.color1+";background:"+_(Bo).userInfo.color1+";opacity:0.8")}," 确定 ",36)])])])):m("",!0),"2"==Ro.memberState?(C(),p("div",Ft,[u("div",At,[t[81]||(t[81]=u("img",{src:ne,class:"memberDetail_head",alt:""},null,-1)),u("div",Tt,[u("div",Pt,[f(b(Ro.memberUserInfo.nickname)+" ",1),u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"memberDetail_tag"},b(Ro.memberUserInfo.level_name),5)]),u("div",Lt,b(Ro.memberUserInfo.tel),1)]),u("div",{onClick:nl,style:v("color:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),class:"memberDetail_btn"}," 切换会员 ",4)]),u("div",Nt,[u("div",qt,[t[82]||(t[82]=u("div",{class:"text"},"余额",-1)),u("div",jt,b(Ro.memberUserInfo.money),1)]),u("div",Et,[u("div",Kt,[t[83]||(t[83]=f(" 本店储值")),u("span",{class:"add_store_val",onClick:t[25]||(t[25]=e=>Ro.storeValState=!0)},"增加储值")]),u("div",Rt,b(Ro.memberUserInfo.stored_money),1)]),u("div",Xt,[t[84]||(t[84]=u("div",{class:"text"},"积分",-1)),u("div",Ot,b(Ro.memberUserInfo.score),1)]),u("div",Qt,[t[85]||(t[85]=u("div",{class:"text"},"优惠券",-1)),u("div",Gt,[f(b(Ro.memberUserInfo.couponcount)+" ",1),0!=Ro.memberUserInfo.couponcount?(C(),p("span",{key:0,onClick:il,style:v("color:"+_(Bo).userInfo.color1),class:"opt"},"查看",4)):m("",!0)])])]),t[93]||(t[93]=u("div",{class:"memberDetail_title"},"会员信息",-1)),u("div",Yt,[u("div",Bt,[t[86]||(t[86]=u("div",{class:"title"},"会员注册时间：",-1)),u("div",Wt,b(Ro.memberUserInfo.createtime||"-"),1)]),u("div",Jt,[t[87]||(t[87]=u("div",{class:"title"},"生日：",-1)),u("div",Zt,b(Ro.memberUserInfo.birthday||"-"),1)]),u("div",Ht,[t[88]||(t[88]=u("div",{class:"title"},"默认收货地址：",-1)),u("div",$t,b(Ro.memberUserInfo.birthday||"-"),1)]),u("div",ea,[t[89]||(t[89]=u("div",{class:"title"},"备注：",-1)),u("div",ta,b(Ro.memberUserInfo.remark||"-"),1)])]),t[94]||(t[94]=u("div",{class:"memberDetail_title"},"消费明细",-1)),u("div",aa,[x(K,{data:Ro.orderList.data,stripe:"",style:{width:"100%"}},{default:y((()=>[x(j,{prop:"ordernum",label:"订单编号"}),x(j,{prop:"createtime",label:"订单时间"}),x(j,{prop:"banace",label:"剩余储值金额"}),x(j,{prop:"money",label:"订单金额"},{default:y((e=>[u("span",{style:v("1"==e.row.pm?"color:#0fb972":"color:#f3939")},b(("1"==e.row.pm?"+":"-")+e.row.money),5)])),_:1}),x(j,{fixed:"right",label:"操作"},{default:y((e=>[x(E,{link:"",type:"primary",size:"small",onClick:t=>{return a=e.row,Ro.orderDetail={},Ro.orderInfoState=!0,void Z(a.id).then((e=>{1===e.status&&(Ro.orderDetail=e.data)}));var a}},{default:y((()=>t[90]||(t[90]=[f(" 查看账单明细 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),t[91]||(t[91]=u("br",null,null,-1)),x(R,{background:!0,layout:"prev, pager, next",onCurrentChange:Yo,"default-page-size":5,total:Number(Ro.orderList.total)},null,8,["total"]),t[92]||(t[92]=u("br",null,null,-1))])])):m("",!0)])):m("",!0),2==Ro.operateIndex?(C(),p("div",oa,[u("div",la,[Ro.dataInfoHang.length?(C(),p("div",ra,[(C(!0),p(g,null,h(Ro.dataInfoHang,((e,a)=>(C(),p("div",{key:a,class:"take_list"},[u("div",ia,"挂单时间："+b(e.hangup_time),1),u("div",sa,[(C(!0),p(g,null,h(e.prolist,((e,t)=>(C(),p("div",{key:t,class:"take_item"},[u("div",da,b(e.proname),1),u("div",na,[u("span",{style:v("color:"+_(Bo).userInfo.color1)},b(_(Qo)+e.sell_price),5),u("span",ca,"x"+b(e.num),1)])])))),128))]),u("div",pa,[u("div",ua,[t[95]||(t[95]=f(" 订单金额：")),u("span",{style:v("color:"+_(Bo).userInfo.color1)},b(_(Qo)+e.pre_totalprice),5)]),u("div",ma,[u("div",{class:"take_btn",style:v("color:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),onClick:t=>gl(e.id)}," 删除 ",12,ba),u("div",{class:"take_btn",style:v("color:#fff;background:"+_(Bo).userInfo.color1),onClick:t=>{return a=e,void z.confirm("您确认取出这个订单吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{if(a.mid){T();let e={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId"),keyword:a.mid};$(e).then((e=>{if("1"==e.status){Ro.memberUserInfo=e.data,Ro.memberState="2",Ro.operateIndex="1";let t={apifrom:"vue",orderid:a.id};ae(t).then((e=>{L(),"1"==e.status?(Sl(),hl()):d.error(e.msg)}))}else d.error(e.msg)}))}else{T();let e={apifrom:"vue",orderid:a.id};ae(e).then((e=>{L(),"1"==e.status?(Sl(),hl()):d.error(e.msg)}))}})).catch((()=>{d({type:"info",message:"已取消"})}));var a}}," 取单 ",12,fa)])])])))),128))])):m("",!0),Ro.dataInfoHang.length?m("",!0):(C(),p("div",xa,t[96]||(t[96]=[u("img",{src:M,class:"take_icon",alt:""},null,-1),u("div",{class:"take_text"},"暂无数据",-1)])))])])):m("",!0),3==Ro.operateIndex?(C(),p("div",ya,[u("div",va,[t[99]||(t[99]=u("div",{class:"key_title"}," 输入收款金额，金额加入结算清单中进行结算。 ",-1)),u("div",_a,[I(u("input",{onFocusin:Tl,onFocusout:Pl,type:"text",class:"key_input",style:v(1==Ro.directFocus?"outline: 1px solid"+_(Bo).userInfo.color1:""),"onUpdate:modelValue":t[26]||(t[26]=e=>Ro.directData=e),placeholder:"请输入需要的金额"},null,36),[[w,Ro.directData]]),t[97]||(t[97]=u("div",{class:"key_unit"},"元",-1))]),u("div",ga,[u("div",ha,[u("div",{onMouseup:Gl,onMousedown:t[27]||(t[27]=e=>jl(7)),class:k(["key_item","7"==Ro.keyIndex?"item_active":""])}," 7 ",34),u("div",{onMouseup:Gl,onMousedown:t[28]||(t[28]=e=>jl(8)),class:k(["key_item","8"==Ro.keyIndex?"item_active":""])}," 8 ",34),u("div",{onMouseup:Gl,onMousedown:t[29]||(t[29]=e=>jl(9)),class:k(["key_item","9"==Ro.keyIndex?"item_active":""])}," 9 ",34),u("div",{onMouseup:Gl,onMousedown:t[30]||(t[30]=e=>jl(4)),class:k(["key_item","4"==Ro.keyIndex?"item_active":""])}," 4 ",34),u("div",{onMouseup:Gl,onMousedown:t[31]||(t[31]=e=>jl(5)),class:k(["key_item","5"==Ro.keyIndex?"item_active":""])}," 5 ",34),u("div",{onMouseup:Gl,onMousedown:t[32]||(t[32]=e=>jl(6)),class:k(["key_item","6"==Ro.keyIndex?"item_active":""])}," 6 ",34),u("div",{onMouseup:Gl,onMousedown:t[33]||(t[33]=e=>jl(1)),class:k(["key_item","1"==Ro.keyIndex?"item_active":""])}," 1 ",34),u("div",{onMouseup:Gl,onMousedown:t[34]||(t[34]=e=>jl(2)),class:k(["key_item","2"==Ro.keyIndex?"item_active":""])}," 2 ",34),u("div",{onMouseup:Gl,onMousedown:t[35]||(t[35]=e=>jl(3)),class:k(["key_item","3"==Ro.keyIndex?"item_active":""])}," 3 ",34),t[98]||(t[98]=u("div",{class:"key_item"},null,-1)),u("div",{onMouseup:Gl,onMousedown:t[36]||(t[36]=e=>jl(0)),class:k(["key_item","0"==Ro.keyIndex?"item_active":""])}," 0 ",34),u("div",{onMouseup:Gl,onMousedown:t[37]||(t[37]=e=>jl(".")),class:k(["key_item","."==Ro.keyIndex?"item_active":""])}," . ",34)]),u("div",ka,[u("div",{onMouseup:Gl,onMousedown:t[38]||(t[38]=e=>jl("clear")),class:k(["key_item key_row","clear"==Ro.keyIndex?"item_active":""])}," 清除 ",34),u("div",{onMouseup:Gl,onMousedown:t[39]||(t[39]=e=>jl("delete")),class:k(["key_item key_row","delete"==Ro.keyIndex?"item_active":""])},[x(l,null,{default:y((()=>[x(q)])),_:1})],34),u("div",{onMouseup:Gl,onMousedown:t[40]||(t[40]=e=>jl("confirm")),class:"key_confirm",style:v("confirm"==Ro.keyIndex?"border-color:"+_(Bo).userInfo.color1+";background:"+_(Bo).userInfo.color1:"border-color:"+_(Bo).userInfo.color1+";background:"+_(Bo).userInfo.color1+";opacity:0.8")}," 确定 ",36)])])])])):m("",!0)])):m("",!0),"1"==Ro.updateState?(C(),p("div",Ia,[u("div",wa,[u("div",Da,[t[100]||(t[100]=u("span",null,"改价",-1)),x(l,{onClick:t[41]||(t[41]=e=>Vl("0",!1)),class:"update_icon"},{default:y((()=>[x(X)])),_:1})]),u("div",Sa,[u("div",za,[x(r,{class:"update_img",src:Ro.shopUpdate.propic},{error:y((()=>[u("div",Ca,[x(l,null,{default:y((()=>[x(_(U))])),_:1})])])),_:1},8,["src"]),u("div",Va,[u("div",Ua,b(Ro.shopUpdate.proname),1),u("div",Ma,[u("span",{style:v("color:"+_(Bo).userInfo.color1)},b(_(Qo)+Ro.shopUpdate.sell_price),5),u("span",Fa,"库存:"+b(Ro.shopUpdate.stock),1)])])]),u("div",Aa,[t[101]||(t[101]=u("div",{class:"update_lable"},"现价",-1)),f(" "+b(_(Qo)+Ro.shopUpdate.sell_price),1)]),u("div",Ta,[t[103]||(t[103]=u("div",{class:"update_lable"},"定价",-1)),u("div",Pa,[I(u("input",{type:"text",class:"update_input",onFocusin:Ll,onFocusout:Nl,style:v(1==Ro.updateFocus?"outline: 1px solid"+_(Bo).userInfo.color1:""),"onUpdate:modelValue":t[42]||(t[42]=e=>Ro.shopUpdate.upPrice=e)},null,36),[[w,Ro.shopUpdate.upPrice]]),t[102]||(t[102]=u("div",{class:"update_unit"},"元",-1))])])]),u("div",La,[u("div",{style:v("color:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),onClick:t[43]||(t[43]=e=>Vl("0",!1)),class:"update_btn flex_xy_center"}," 取消 ",4),u("div",{style:v("color:#fff;background:"+_(Bo).userInfo.color1),onClick:kl,class:"update_btn flex_xy_center"}," 确定 ",4)])])])):m("",!0),"2"==Ro.updateState||"3"==Ro.updateState?(C(),p("div",Na,[u("div",qa,[u("div",ja,["2"==Ro.updateState?(C(),p("div",Ea,[u("div",Ka,[t[104]||(t[104]=u("img",{src:ne,class:"pay_img",alt:""},null,-1)),u("div",null,[u("div",Ra,b(Ro.memberUserInfo.nickname),1),u("div",Xa,b(Ro.memberUserInfo.tel),1)]),u("div",{onClick:nl,style:v("color:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),class:"pay_change"}," 切换会员 ",4)]),u("div",Oa,[t[105]||(t[105]=u("div",{class:"title"},"会员积分",-1)),u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"num"},b(Ro.memberUserInfo.score),5)]),u("div",Qa,[t[106]||(t[106]=u("div",{class:"title"},"会员余额",-1)),u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"num"},b(_(Qo)+Ro.memberUserInfo.money),5)]),u("div",Ga,[t[107]||(t[107]=u("div",{class:"title"},"本店储值",-1)),u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"num"},b(_(Qo)+Ro.memberUserInfo.stored_money),5)])])):m("",!0),u("div",Ya,[u("div",Ba,[u("div",Wa," 收款金额："+b(_(Qo)+Ro.table.order.totalprice),1),u("div",Ja," 服务费："+b(_(Qo)+Number(Ro.table.order.server_fee).toFixed(2)),1),u("div",Za," 茶位费："+b(_(Qo)+Number(Ro.table.order.tea_fee).toFixed(2)),1),u("div",Ha," 税金："+b(_(Qo)+Number(Ro.table.order.taxes).toFixed(2)),1),u("div",$a,[f(" 优惠："+b("-"+_(Qo))+" ",1),x(i,{modelValue:Go.discountVal,"onUpdate:modelValue":t[44]||(t[44]=e=>Go.discountVal=e),min:0,max:Ro.table.order.totalprice,style:{width:"120px"}},null,8,["modelValue","max"])]),Ro.payInfo.couponlist.length?(C(),p("div",eo,[t[108]||(t[108]=u("div",{class:"pay_name"},"优惠券：",-1)),u("div",to,[x(Q,{wrap:""},{default:y((()=>[(C(!0),p(g,null,h(Ro.payInfo.couponlist,((e,t)=>(C(),V(O,{class:"pay_card",style:v(Ro.payData.couponid==e.id?"color:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1:""),onClick:t=>(e=>{Ro.payData.couponid=e.id})(e),key:t,shadow:"always"},{default:y((()=>[u("div",ao,b(e.couponname),1),u("div",oo,"有效期："+b(e.endtime),1)])),_:2},1032,["style","onClick"])))),128))])),_:1})])])):m("",!0),Ro.memberUserInfo.id&&0!=Ro.payInfo.memberinfo.score?(C(),p("div",lo,[f(b(Ro.payInfo.memberinfo.score)+"积分： ",1),x(G,{style:{"margin-right":"3px"},onChange:t[45]||(t[45]=e=>{return t=e,void(Ro.payData.userscore=t?"1":"0");var t}),label:"使用积分抵扣"})])):m("",!0),u("div",ro," 实付金额："+b(_(Qo)+$o.value),1),u("div",io,[t[109]||(t[109]=u("div",{class:"title"},"支付方式",-1)),u("div",so,[(C(!0),p(g,null,h(Ro.payTypeListMember,((e,t)=>(C(),p("div",{key:t,class:"pay_type_item",style:v(Ro.payData.paytype==e.value?"color:#fff;background:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1:""),onClick:t=>Ro.payData.paytype=e.value},b(e.lable),13,no)))),128))])])]),u("div",co,[u("div",po,[t[110]||(t[110]=u("span",null,"会员折扣",-1)),u("span",uo,"-"+b(_(Qo)+el.value),1)]),u("div",mo,[u("div",bo,[I(u("input",{type:"text",onKeyup:Rl,onKeydown:S(Xl,["stop"]),"onUpdate:modelValue":t[46]||(t[46]=e=>Go.discount=e),maxLength:4},null,544),[[w,Go.discount]]),u("img",{onClick:El,class:"pay_discount_delete",src:"./static/delete.df3f82fd.png"})]),u("div",fo,[(C(!0),p(g,null,h(Oo,((e,t)=>(C(),p("div",{class:k(Go.keyIndex==e?"active":""),onMouseup:Kl,onMousedown:t=>Ql(e),key:t},b(e),43,xo)))),128))])])])])]),u("div",yo,[u("div",{onClick:dl,style:v("color:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),class:"pay_btn flex_xy_center"}," 取消 ",4),u("div",{onClick:rl,style:v("color:#fff;background:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),class:"pay_btn flex_xy_center"}," 确定付款 ",4)])])])):m("",!0),"4"==Ro.updateState?(C(),p("div",vo,[u("div",_o,[u("div",go,[x(l,{style:v("color:"+_(Bo).userInfo.color1),class:"icon"},{default:y((()=>[x(Y)])),_:1},8,["style"]),u("div",{style:v("color:"+_(Bo).userInfo.color1),class:"text"}," 收款成功 ",4)]),u("div",ho,[u("div",{onClick:ol,style:v("color:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),class:"success_btn flex_xy_center"}," 打印小票 ",4),u("div",{onClick:ll,style:v("color:#fff;background:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1),class:"success_btn flex_xy_center"}," 继续收款 ",4)])])])):m("",!0),x(J,{modelValue:Ro.dialogState,"onUpdate:modelValue":t[49]||(t[49]=e=>Ro.dialogState=e),title:"选择规格",width:"500px"},{footer:y((()=>[u("span",ko,[x(E,{onClick:t[48]||(t[48]=e=>Ro.dialogState=!1)},{default:y((()=>t[111]||(t[111]=[f("取消")]))),_:1}),x(E,{onClick:Dl,type:"primary"},{default:y((()=>t[112]||(t[112]=[f("确定")]))),_:1})])])),default:y((()=>[x(W,{model:Ro},{default:y((()=>[x(B,{label:"规格","label-width":"200"},{default:y((()=>[x(N,{modelValue:Ro.specsInfo.ggid,"onUpdate:modelValue":t[47]||(t[47]=e=>Ro.specsInfo.ggid=e)},{default:y((()=>[(C(!0),p(g,null,h(Ro.specsInfo.guigelist,((e,t)=>(C(),V(P,{key:t,label:e.id,border:""},{default:y((()=>[f(b(e.name),1)])),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),x(J,{modelValue:Ro.remarkState,"onUpdate:modelValue":t[53]||(t[53]=e=>Ro.remarkState=e),title:"添加备注",width:"500px"},{footer:y((()=>[u("span",Io,[x(E,{onClick:t[51]||(t[51]=e=>Ro.remarkState=!1)},{default:y((()=>t[113]||(t[113]=[f("取消")]))),_:1}),x(E,{onClick:t[52]||(t[52]=e=>fl()),type:"primary"},{default:y((()=>t[114]||(t[114]=[f("确定")]))),_:1})])])),default:y((()=>[x(W,{model:Ro},{default:y((()=>[x(B,{label:"备注","label-width":"120"},{default:y((()=>[x(s,{modelValue:Ro.dataInfo.remark,"onUpdate:modelValue":t[50]||(t[50]=e=>Ro.dataInfo.remark=e),autosize:{minRows:6,maxRows:4},maxlength:"200","show-word-limit":"",type:"textarea"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),x(J,{modelValue:Ro.couponInfoState,"onUpdate:modelValue":t[56]||(t[56]=e=>Ro.couponInfoState=e),title:"优惠券列表",width:"550px"},{footer:y((()=>[u("span",wo,[x(E,{onClick:t[54]||(t[54]=e=>Ro.couponInfoState=!1)},{default:y((()=>t[115]||(t[115]=[f("取消")]))),_:1}),x(E,{onClick:t[55]||(t[55]=e=>Ro.couponInfoState=!1),type:"primary"},{default:y((()=>t[116]||(t[116]=[f("确定")]))),_:1})])])),default:y((()=>[x(W,{model:Ro},{default:y((()=>[x(B,{label:"优惠券","label-width":"200"},{default:y((()=>[x(Q,{wrap:""},{default:y((()=>[(C(!0),p(g,null,h(Ro.couponInfo,((e,t)=>(C(),V(O,{key:t,shadow:"always"},{default:y((()=>[u("div",null,b(e.couponname),1),u("div",null,"有效期："+b(e.endtime),1)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),x(J,{modelValue:Ro.storeValState,"onUpdate:modelValue":t[59]||(t[59]=e=>Ro.storeValState=e),title:"增加储值",width:"550px"},{footer:y((()=>[u("span",Vo,[x(E,{onClick:t[58]||(t[58]=e=>Ro.storeValState=!1)},{default:y((()=>t[119]||(t[119]=[f("取消")]))),_:1}),x(E,{onClick:ml,type:"primary"},{default:y((()=>t[120]||(t[120]=[f("确定")]))),_:1})])])),default:y((()=>[x(W,{model:Ro},{default:y((()=>[u("div",Do,[t[117]||(t[117]=u("label",null,"输入金额",-1)),x(i,{modelValue:Ro.storeVal.value,"onUpdate:modelValue":t[57]||(t[57]=e=>Ro.storeVal.value=e),autocomplete:"off",min:"1"},null,8,["modelValue"])]),u("div",So,[t[118]||(t[118]=u("label",null,"支付方式",-1)),u("div",zo,[(C(!0),p(g,null,h(Ro.payTypeListStoreVal,((e,t)=>(C(),p("div",{key:t,class:"pay_type_item",style:v(Ro.storeVal.paytype==e.value?"color:#fff;background:"+_(Bo).userInfo.color1+";border-color:"+_(Bo).userInfo.color1:""),onClick:t=>Ro.storeVal.paytype=e.value},b(e.lable),13,Co)))),128))])])])),_:1},8,["model"])])),_:1},8,["modelValue"]),x(J,{modelValue:Ro.orderInfoState,"onUpdate:modelValue":t[61]||(t[61]=e=>Ro.orderInfoState=e),title:"订单明细",width:"550px"},{footer:y((()=>[u("span",No,[x(E,{onClick:t[60]||(t[60]=e=>Ro.orderInfoState=!1),type:"primary"},{default:y((()=>t[128]||(t[128]=[f("确定")]))),_:1})])])),default:y((()=>[u("div",Uo,[u("div",Mo,[t[121]||(t[121]=u("label",null,"订单编号",-1)),u("span",null,b(Ro.orderDetail.ordernum),1)]),u("div",Fo,[t[122]||(t[122]=u("label",null,"订单时间",-1)),u("span",null,b(_(de)(Ro.orderDetail.createtime)),1)]),t[127]||(t[127]=u("hr",null,null,-1)),u("div",Ao,[t[123]||(t[123]=u("label",null,"类型",-1)),u("span",null,b("1"==Ro.orderDetail.pm?"充值":"扣除"),1)]),u("div",To,[t[124]||(t[124]=u("label",null,"支付方式",-1)),u("span",null,b(Ro.orderDetail.paytype),1)]),u("div",Po,[t[125]||(t[125]=u("label",null,"订单金额",-1)),u("span",{style:v("1"==Ro.orderDetail.pm?"color:#0fb972":"color:#f3939")},b(("1"==Ro.orderDetail.pm?"+":"-")+(Ro.orderDetail.money||0)),5)]),u("div",Lo,[t[126]||(t[126]=u("label",null,"剩余储值金额",-1)),u("span",null,b(Ro.orderDetail.banace),1)])])])),_:1},8,["modelValue"]),x(J,{modelValue:Ro.tableListState,"onUpdate:modelValue":t[63]||(t[63]=e=>Ro.tableListState=e),title:"选择桌台",width:"1080px"},{footer:y((()=>[u("span",Ko,[x(E,{onClick:t[62]||(t[62]=e=>Ro.tableListState=!1)},{default:y((()=>t[130]||(t[130]=[f("取消")]))),_:1})])])),default:y((()=>[u("div",qo,[(C(!0),p(g,null,h(Ro.table.free,(e=>(C(),p("div",{class:"table_item bg-green",onClick:t=>(e=>{if(Ro.table.select==e)return void d.error("所换桌号相同");let t={origin:Ro.table.select,new:e};ee(t).then((t=>{1==t.status?(d.success("换桌成功"),Jo(`/index/index?tid=${e}`),Ho(e),Ro.table.select=e):d.error(t.msg)})),Ro.tableListState=!1,Ro.table.free=[]})(e.id),key:e.id},[u("span",Eo,"桌号: "+b(e.name),1),u("span",null,"座位: "+b(e.seat),1),t[129]||(t[129]=u("span",null,"空闲",-1))],8,jo)))),128))])])),_:1},8,["modelValue"])])}}});Ro.__scopeId="data-v-3c13e60b";export{Ro as default};
