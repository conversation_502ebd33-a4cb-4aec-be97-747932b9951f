// 导入axios实例
import httpRequest from '../../utils/axios'

// 1.商品列表
interface getProductParam {
	page: any,
	limit: any,
	name: any,
	code: any,
	cid: any,
	apifrom: any
}
export function getProductList(param: getProductParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/getProductList',
		method: 'post',
		data: param,
	})
}

// 2.商品分类
interface getCategoryParam {
	page: any,
	limit: any,
	apifrom: any
}
export function getCategoryList(param: getCategoryParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/getCategoryList',
		method: 'post',
		data: param,
	})
}

// 3.订单列表
interface getCashierParam {
	cashier_id: any,
	uid: any,
	status: any
}
export function getCashierOrder(param: getCashierParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/getCashierOrder',
		method: 'post',
		data: param,
	})
}

// 4.商品加入收银台
interface addToCashierParam {
	cashier_id: any,
	proid: any,
	num: any,
	price: any
}
export function addToCashier(param: addToCashierParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/addToCashier',
		method: 'post',
		data: param,
	})
}

// 5.收银台商品数量增减
interface changeNumParam {
	cashier_id: any,
	proid: any,
	num: any
}
export function cashierChangeNum(param: changeNumParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/cashierChangeNum',
		method: 'post',
		data: param,
	})
}

// 6.改价
interface cashierChangePriceParam {
	cashier_id: any,
	proid: any,
	price: any
}
export function cashierChangePrice(param: cashierChangePriceParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/cashierChangePrice',
		method: 'post',
		data: param,
	})
}

// 7.挂单
interface hangupParam {
	cashier_id: any
}
export function hangup(param: hangupParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/hangup',
		method: 'post',
		data: param,
	})
}

// 8.取单
interface cancelParam {
	orderid: any
}
export function cancelHangup(param: cancelParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/cancelHangup',
		method: 'post',
		data: param,
	})
}

// 9.订单删除
interface delCashierParam {
	orderid: any
}
export function delCashierOrder(param: delCashierParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/delCashierOrder',
		method: 'post',
		data: param,
	})
}

// 10.订单备注
interface cashierChangeRemarkParam {
	orderid: any,
	remark: any
}
export function cashierChangeRemark(param: cashierChangeRemarkParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/cashierChangeRemark',
		method: 'post',
		data: param,
	})
}

// 11.通过 [手机号/mid] 查询取用户信息
interface searchMemberParam {
	keyword: any
}
export function searchMember(param: searchMemberParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/searchMember',
		method: 'post',
		data: param,
	})
}

// 12.待支付订单
interface getWaitPayOrderParam {
	cashier_id: any,
	remove_zero: any
}
export function getWaitPayOrder(param: getWaitPayOrderParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/getWaitPayOrder',
		method: 'post',
		data: param,
	})
}

// 13.会员优惠券列表
interface memberCouponListParam {
	page: any,
	limit : any,
	mid : any
}
export function memberCouponList(param: memberCouponListParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/memberCouponList',
		method: 'post',
		data: param,
	})
}

// 14.支付预览
interface payPreviewParam {
	cashier_id: any,
	mid : any
}
export function payPreview(param: payPreviewParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/payPreview',
		method: 'post',
		data: param,
	})
}

// 15.支付
interface payParam {
	cashier_id: any,
	couponid: any,
	mid: any,
	userscore: any,
	paytype: any
}
export function payCashier(param: payParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/pay',
		method: 'post',
		data: param,
	})
}

// 16.打印小票
interface printParam {
	orderid: any
}
export function printBusiness(param: printParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/print',
		method: 'post',
		data: param,
	})
}

// 17.收银台配置
interface getCashierInfoParam {
	cashier_id: any
}
export function getCashierInfo(param: getCashierInfoParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/getCashierInfo',
		method: 'post',
		data: param,
	})
}

interface registerMemberParam {
	realname: string,
	tel: string,
	birthday: string,
	sex: number,
	stored_val: number
}
export function registerMember(param:registerMemberParam) {
	return httpRequest({
		url: '/?s=/RestaurantCashier/registerMember',
		method: 'post',
		data: param
	})
}

// 充值
interface storeRechargeParam {
	stored_val: any,
	uid: any,
	paytype: any,
	pm: any // 1:充值 2:划扣
}
export function storeRecharge(param: storeRechargeParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/storeRecharge',
		method: 'post',
		data: param,
	})
}

// 储值历史
interface getStoredParam {
	uid: any
	page: any,
	limit: any
}
export function getStored(param: getStoredParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/getStored',
		method: 'post',
		data: param,
	})
}
// 储值详情
export function getStoredDetail(id: string) {
    return httpRequest({
		url: '/?s=/RestaurantCashier/getStoredDetail&id=' + id,
		method: 'get'
	})
}

// 交班
interface jiaobanParam {
	datetime: any
}
export function jiaoban(param: jiaobanParam) {
    return httpRequest({
		url: '/?s=/RestaurantCashierLogin/jiaoban',
		method: 'post',
		data: param,
	})
}

// 桌台列表
export function getTableList() {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/tablelist',
		method: 'post'
	})
}

// 清理桌台
export function cleanTable(tableId: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/clean',
		method: 'post',		
		data: {tableId},
	})
}

// 清台完成
export function cleanOver(tableId: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/cleanOver',
		method: 'post',		
		data: {tableId},
	})
}
// 换桌
export function changeTable(params: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/change',
		method: 'post',
		data: params
	})
}

// 下单
export function createOrder(params: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/createOrder',
		method: 'post',
		data: params,
	})
}

// 加菜
export function editOrder(params: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/editOrder',
		method: 'post',
		data: params,
	})
}

export function buyview(tableId: any, prodata?: any) {
	return httpRequest({
		url: `/?s=/RestaurantCashierLogin/buyview&tableId=${tableId}&frompage=admin&prodata=${prodata}`,
		method: 'get'
	})
}

// 收款
export function payconfirm(params: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/payconfirm',
		method: 'post',
		data: params,
	})
}

// 桌台信息
export function tableInfo(tableId: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/detail&id=' + tableId,
		method: 'get'
	})
}

// 点菜列表（购物车）
export function chooselist(tableId: any, bid: any) {
	return httpRequest({
		url: `/?s=/RestaurantCashierLogin/chooselist&tableId=${tableId}&bid=${bid}`,
		method: 'get'
	})
}

// 加入购物车
export function addcart(params: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/addcart',
		method: 'post',
		data: params,
	})
}

// 删除购物车菜品
export function cartdelete(params: any) {
	return httpRequest({
		url: '/?s=/RestaurantCashierLogin/cartdelete',
		method: 'post',
		data: params,
	})
}

// 清空购物车
export function cartclear(tableId: any) {
	return httpRequest({
		url: `/?s=/RestaurantCashierLogin/cartclear&tableId=${tableId}`,
		method: 'get'
	})
}


export function orderlist(params: any) {
	return httpRequest({
		url: `/?s=/RestaurantCashier/orderlist`,
		method: 'post',
		data: params,
	})
}