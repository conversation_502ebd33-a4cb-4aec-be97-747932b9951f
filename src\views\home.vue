<template>
  <div class="header">
    <div class="menu flex_y_center">
      <img :src="form.userInfo.blogo" alt="" />
      <div class="title flex_x1">{{ form.userInfo.name }}</div>
      <el-select :placeholder="$t('header.langLable')" style="width: 150px">
        <el-option
          :label="$t('header.langZH')"
          value="zh"
          @click="changeLanguage('zh')"
        />
        <el-option
          :label="$t('header.langEN')"
          value="en"
          @click="changeLanguage('en')"
        />
        <el-option
          :label="$t('header.langTH')"
          value="th"
          @click="changeLanguage('th')"
        />
      </el-select>
      <div class="user">
        <el-dropdown>
          <span class="el-dropdown-link">
            操作员（{{ form.userInfo.option_name }}）<el-icon
              class="el-icon--right"
            >
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="toJiaoban">交班信息</el-dropdown-item>
              <el-dropdown-item @click="loginOut">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
  <div class="body">
    <div class="module flex">
      <div class="nav">
        <div
          @click="toPath('/index/index')"
          class="item"
          :style="
            form.currentPath == '/index/index'
              ? 'color:' +
                storeInfo.userInfo.color1 +
                ';background:rgba(' +
                storeInfo.userInfo.color1rgb.red +
                ',' +
                storeInfo.userInfo.color1rgb.green +
                ',' +
                storeInfo.userInfo.color1rgb.blue +
                ',0.2)'
              : ''
          "
        >
          {{ $t("sidebar.home") }}
        </div>
        <div
          @click="toPath('/tables/index')"
          class="item"
          :style="
            form.currentPath == '/tables/index'
              ? 'color:' +
                storeInfo.userInfo.color1 +
                ';background:rgba(' +
                storeInfo.userInfo.color1rgb.red +
                ',' +
                storeInfo.userInfo.color1rgb.green +
                ',' +
                storeInfo.userInfo.color1rgb.blue +
                ',0.2)'
              : ''
          "
        >
          {{ $t("sidebar.table") }}
        </div>
        <div
          @click="toPath('/order/index')"
          class="item"
          :style="
            form.currentPath == '/order/index'
              ? 'color:' +
                storeInfo.userInfo.color1 +
                ';background:rgba(' +
                storeInfo.userInfo.color1rgb.red +
                ',' +
                storeInfo.userInfo.color1rgb.green +
                ',' +
                storeInfo.userInfo.color1rgb.blue +
                ',0.2)'
              : ''
          "
        >
          {{ $t("sidebar.order") }}
        </div>
      </div>
      <div class="main flex_x1">
        <router-view />
      </div>
    </div>
  </div>

  <el-dialog v-model="dialogFormVisible" width="500" top="50px">
    <template #header>
      <span class="dialog_title">交班信息</span>
      <span class="dialog_history">历史信息</span>
      <span class="dialog_date">
        <el-date-picker
          @change="changeHandoverDate"
          v-model="handoverInfo.date"
          type="date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="选择时间"
        />
      </span>
    </template>

    <div class="handover_info">
      <div class="handover_item">
        <label>当前账号</label
        ><span>{{ handoverInfo.data.cashdesk_user }}</span>
      </div>
      <div class="handover_item">
        <label>登录时间</label><span>{{ handoverInfo.data.logintime }}</span>
      </div>
      <div class="handover_item">
        <label>交班时间</label><span>{{ handoverInfo.data.jiaobantime }}</span>
      </div>
      <hr />
      <div class="handover_item">
        <label>订单总数</label><span>{{ handoverInfo.data.ordercount }}</span>
      </div>
      <div class="handover_item">
        <label>进店人数</label><span>{{ 112211211 }}</span>
      </div>
      <div class="handover_item">
        <label>总商品成本</label><span>{{ 112211211 }}</span>
      </div>
      <div class="handover_item">
        <label>毛利润</label><span>{{ 112211211 }}</span>
      </div>
      <div class="handover_item">
        <label>人均消费</label><span>{{ 112211211 }}</span>
      </div>
      <div class="handover_item">
        <label>折扣</label><span>{{ 112211211 }}</span>
      </div>
      <div class="handover_item">
        <label>抹零</label><span>{{ 112211211 }}</span>
      </div>
      <hr />
      <div class="handover_item">
        <label>今日营业额总收款</label
        ><span>{{ handoverInfo.data.today_total_money }}</span>
      </div>
      <div class="handover_item">
        <label>现金</label><span>{{ handoverInfo.data.today_cash_money }}</span>
      </div>
      <div class="handover_item">
        <label>余额</label><span>{{ handoverInfo.data.today_yue_money }}</span>
      </div>
      <div class="handover_item">
        <label>本店储值</label><span>{{ 112211211 }}</span>
      </div>
      <div class="handover_item">
        <label>微信</label><span>{{ handoverInfo.data.today_wx_money }}</span>
      </div>
      <div class="handover_item">
        <label>支付宝</label
        ><span>{{ handoverInfo.data.today_alipay_money }}</span>
      </div>
      <div class="handover_item">
        <label>QR</label><span>{{ 112211211 }}</span>
      </div>
      <hr />
      <div class="handover_item">
        <label>今日储值</label><span>{{ 112211211 }}</span>
      </div>
      <div class="handover_item">
        <label>消费</label><span>{{ 112211211 }}</span>
      </div>
      <div class="handover_item">
        <label>退款总额</label
        ><span>{{ handoverInfo.data.today_refund_total_money }}</span>
      </div>
      <hr />
      <div class="handover_item">
        <label>今日总收款</label><span class="total">{{ 112211211 }}</span>
      </div>
      <div class="handover_print">
        <el-switch
          v-model="handoverInfo.print"
          size="large"
          active-text="打印小票"
        />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary"> 打印 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { watch, reactive, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import { getCashierInfo, jiaoban } from "../assets/apis/api";
import { openLoading, closeLoading } from "../utils/loading";
import { userStore } from "../store/userInfo";
import { useI18n } from "vue-i18n";
const { locale } = useI18n();

const changeLanguage = (value: string) => {
  locale.value = value;
};

const dialogFormVisible = ref(false);
const form: any = reactive({
  currentPath: "",
  userInfo: {},
});
const currentDate = new Date();
const year = currentDate.getFullYear();
const month = currentDate.getMonth() + 1;
const day = currentDate.getDate();
const date = `${year}-${month}-${day}`;
const handoverInfo: any = reactive({
  date: date,
  print: 1,
  data: {},
});
const router = useRouter();
const storeInfo = userStore();
onMounted(() => {
  if (router.currentRoute.value.query.id) {
    let cashier_id: any = router.currentRoute.value.query.id;
    sessionStorage.setItem("cashierId", cashier_id);
  }
  togetCashierInfo();
});
const togetCashierInfo = () => {
  openLoading();
  let param: any = {
    apifrom: "vue",
    cashier_id: sessionStorage.getItem("cashierId"),
  };
  getCashierInfo(param).then((res: any) => {
    closeLoading();
    if (res.status == "1") {
      form.userInfo = res.data;
      storeInfo.setuserInfo(res.data);
      var link: any = {};
      link =
        document.querySelector("link[rel*='icon']") ||
        document.createElement("link");
      link.type = "image/x-icon";
      link.rel = "shortcut icon";
      link.href = res.data.ico;
      document.getElementsByTagName("head")[0].appendChild(link);
    } else {
      ElMessage.error(res.msg);
    }
  });
};

const doJiaoban = (datetime: any) => {
  jiaoban({ datetime }).then((res) => {
    console.log(res);
    if (res.status == 1) {
      handoverInfo.data = res.data;
    }
  });
};
const changeHandoverDate = (val: any) => {
  if (val) {
    doJiaoban(val);
  }
};
const toJiaoban = () => {
  dialogFormVisible.value = true;
  doJiaoban(handoverInfo.date);
};

const loginOut = () => {
  ElMessageBox.confirm("退出登录?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    distinguishCancelAndClose: true,
    type: "warning",
    callback: function (action: any) {
      if (action == "confirm") {
        window.location.href =
          window.location.origin + "/?s=/RestaurantCashierLogin/index";
      }
    },
  });
};
watch(
  () => router.currentRoute.value.path,
  (toPath: any) => {
    form.currentPath = toPath;
  },
  { immediate: true, deep: true }
);

const toPath = (path: any) => {
  router.replace(path);
};
</script>
<style lang="scss">
.el-date-editor.el-input {
  width: 150px;
}
</style>

<style scoped lang="scss">
.dialog_title {
  padding-right: 50px;
}
.dialog_history {
  padding-right: 10px;
}
.dialog_date {
  display: inline-block;
  width: 100px;
}
hr {
  border: none;
  height: 1px;
  background-color: #ccc;
}
.handover_info {
  padding: 0 20px;
  .handover_item {
    padding: 5px 0;
    label {
      display: inline-block;
      width: 150px;
    }
    .total {
      font-size: 20px;
      font-weight: bold;
    }
  }
  .handover_print {
    text-align: right;
  }
}
.header {
  width: 100%;
  background: #fff;

  .menu {
    background: #fff;
    height: 65px;
    max-width: 1627px;
    width: 100%;
    margin: 0 auto;

    img {
      height: 42px;
      border-radius: 5px;
      margin-right: 15px;
    }
  }

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }

  .user {
    cursor: pointer;
    font-size: 14px;
    color: #6b6f7a;
  }
}

.body {
  width: 100%;
  background-color: #f7f7f7;
  padding: 20px 0;
  box-sizing: border-box;
  height: calc(100vh - 65px);

  .module {
    max-width: 1627px;
    width: 100%;
    height: 100%;
    margin: 0 auto;
  }

  .nav {
    width: 72px;
    height: 100%;
    background: #fff;
    border-right: 1px solid #f2f2f2;
  }

  .item {
    height: 64px;
    background: #fff;
    border-bottom: 1px solid #f2f2f2;
    line-height: 64px;
    text-align: center;
    cursor: pointer;
    font-size: 16px;
    color: #333;
  }

  .main {
    height: 100%;
  }
}
</style>
