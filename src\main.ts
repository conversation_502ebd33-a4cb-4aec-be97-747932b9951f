import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router/index'
import './assets/css/common.css'
import './assets/css/scrollBar.css'
import store from './store/index'
import i18n from './language/i18n'

const app = createApp(App)
app.use(i18n) // 挂载语言包
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.use(ElementPlus).use(router).use(store).mount('#app')