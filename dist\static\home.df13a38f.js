let a=document.createElement("style");a.innerHTML=".el-date-editor.el-input{width:150px}.dialog_title[data-v-254aa0c0]{padding-right:50px}.dialog_history[data-v-254aa0c0]{padding-right:10px}.dialog_date[data-v-254aa0c0]{display:inline-block;width:100px}hr[data-v-254aa0c0]{border:none;height:1px;background-color:#ccc}.handover_info[data-v-254aa0c0]{padding:0 20px}.handover_info .handover_item[data-v-254aa0c0]{padding:5px 0}.handover_info .handover_item label[data-v-254aa0c0]{display:inline-block;width:150px}.handover_info .handover_item .total[data-v-254aa0c0]{font-size:20px;font-weight:700}.handover_info .handover_print[data-v-254aa0c0]{text-align:right}.header[data-v-254aa0c0]{width:100%;background:#fff}.header .menu[data-v-254aa0c0]{background:#fff;height:65px;max-width:1627px;width:100%;margin:0 auto}.header .menu img[data-v-254aa0c0]{height:42px;border-radius:5px;margin-right:15px}.header .title[data-v-254aa0c0]{font-size:16px;font-weight:700;color:#333}.header .user[data-v-254aa0c0]{cursor:pointer;font-size:14px;color:#6b6f7a}.body[data-v-254aa0c0]{width:100%;background-color:#f7f7f7;padding:20px 0;box-sizing:border-box;height:calc(100vh - 65px)}.body .module[data-v-254aa0c0]{max-width:1627px;width:100%;height:100%;margin:0 auto}.body .nav[data-v-254aa0c0]{width:72px;height:100%;background:#fff;border-right:1px solid #f2f2f2}.body .item[data-v-254aa0c0]{height:64px;background:#fff;border-bottom:1px solid #f2f2f2;line-height:64px;text-align:center;cursor:pointer;font-size:16px;color:#333}.body .main[data-v-254aa0c0]{height:100%}",document.head.appendChild(a);import{d as e,u as l,r as n,a as t,b as o,o as d,E as r,w as i,c as s,e as u,f as c,t as h,g as v,h as p,n as m,i as g,F as f,j as b,k as _,l as x}from"./index.87a9fe81.js";import{o as y,g as w,c as k,j as I}from"./api.bd37d314.js";import{u as C}from"./userInfo.76997c16.js";const $={class:"header"},V={class:"menu flex_y_center"},Y=["src"],z={class:"title flex_x1"},j={class:"user"},D={class:"el-dropdown-link"},M={class:"body"},E={class:"module flex"},P={class:"nav"},R={class:"main flex_x1"},T={class:"dialog_date"},q={class:"handover_info"},B={class:"handover_item"},H={class:"handover_item"},L={class:"handover_item"},S={class:"handover_item"},U={class:"handover_item"},F={class:"handover_item"},N={class:"handover_item"},A={class:"handover_item"},Q={class:"handover_item"},Z={class:"handover_item"},G={class:"handover_print"},J={class:"dialog-footer"};var K=e({__name:"home",setup(a){const{locale:e}=l(),K=a=>{e.value=a},O=n(!1),W=t({currentPath:"",userInfo:{}}),X=new Date,aa=X.getFullYear(),ea=X.getMonth()+1,la=X.getDate(),na=t({date:`${aa}-${ea}-${la}`,print:1,data:{}}),ta=o(),oa=C();d((()=>{if(ta.currentRoute.value.query.id){let a=ta.currentRoute.value.query.id;sessionStorage.setItem("cashierId",a)}da()}));const da=()=>{y();let a={apifrom:"vue",cashier_id:sessionStorage.getItem("cashierId")};w(a).then((a=>{if(k(),"1"==a.status){W.userInfo=a.data,oa.setuserInfo(a.data);var e={};(e=document.querySelector("link[rel*='icon']")||document.createElement("link")).type="image/x-icon",e.rel="shortcut icon",e.href=a.data.ico,document.getElementsByTagName("head")[0].appendChild(e)}else r.error(a.msg)}))},ra=a=>{I({datetime:a}).then((a=>{console.log(a),1==a.status&&(na.data=a.data)}))},ia=a=>{a&&ra(a)},sa=()=>{O.value=!0,ra(na.date)},ua=()=>{x.confirm("退出登录?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",distinguishCancelAndClose:!0,type:"warning",callback:function(a){"confirm"==a&&(window.location.href=window.location.origin+"/?s=/RestaurantCashierLogin/index")}})};i((()=>ta.currentRoute.value.path),(a=>{W.currentPath=a}),{immediate:!0,deep:!0});const ca=a=>{ta.replace(a)};return(a,e)=>{const l=s("el-option"),n=s("el-select"),t=s("arrow-down"),o=s("el-icon"),d=s("el-dropdown-item"),r=s("el-dropdown-menu"),i=s("el-dropdown"),x=s("router-view"),y=s("el-date-picker"),w=s("el-switch"),k=s("el-button"),I=s("el-dialog");return b(),u(f,null,[c("div",$,[c("div",V,[c("img",{src:W.userInfo.blogo,alt:""},null,8,Y),c("div",z,h(W.userInfo.name),1),v(n,{placeholder:a.$t("header.langLable"),style:{width:"150px"}},{default:p((()=>[v(l,{label:a.$t("header.langZH"),value:"zh",onClick:e[0]||(e[0]=a=>K("zh"))},null,8,["label"]),v(l,{label:a.$t("header.langEN"),value:"en",onClick:e[1]||(e[1]=a=>K("en"))},null,8,["label"]),v(l,{label:a.$t("header.langTH"),value:"th",onClick:e[2]||(e[2]=a=>K("th"))},null,8,["label"])])),_:1},8,["placeholder"]),c("div",j,[v(i,null,{dropdown:p((()=>[v(r,null,{default:p((()=>[v(d,{onClick:sa},{default:p((()=>e[10]||(e[10]=[_("交班信息")]))),_:1}),v(d,{onClick:ua},{default:p((()=>e[11]||(e[11]=[_("退出登录")]))),_:1})])),_:1})])),default:p((()=>[c("span",D,[_(" 操作员（"+h(W.userInfo.option_name)+"）",1),v(o,{class:"el-icon--right"},{default:p((()=>[v(t)])),_:1})])])),_:1})])])]),c("div",M,[c("div",E,[c("div",P,[c("div",{onClick:e[3]||(e[3]=a=>ca("/index/index")),class:"item",style:m("/index/index"==W.currentPath?"color:"+g(oa).userInfo.color1+";background:rgba("+g(oa).userInfo.color1rgb.red+","+g(oa).userInfo.color1rgb.green+","+g(oa).userInfo.color1rgb.blue+",0.2)":"")},h(a.$t("sidebar.home")),5),c("div",{onClick:e[4]||(e[4]=a=>ca("/tables/index")),class:"item",style:m("/tables/index"==W.currentPath?"color:"+g(oa).userInfo.color1+";background:rgba("+g(oa).userInfo.color1rgb.red+","+g(oa).userInfo.color1rgb.green+","+g(oa).userInfo.color1rgb.blue+",0.2)":"")},h(a.$t("sidebar.table")),5),c("div",{onClick:e[5]||(e[5]=a=>ca("/order/index")),class:"item",style:m("/order/index"==W.currentPath?"color:"+g(oa).userInfo.color1+";background:rgba("+g(oa).userInfo.color1rgb.red+","+g(oa).userInfo.color1rgb.green+","+g(oa).userInfo.color1rgb.blue+",0.2)":"")},h(a.$t("sidebar.order")),5)]),c("div",R,[v(x)])])]),v(I,{modelValue:O.value,"onUpdate:modelValue":e[9]||(e[9]=a=>O.value=a),width:"500",top:"50px"},{header:p((()=>[e[12]||(e[12]=c("span",{class:"dialog_title"},"交班信息",-1)),e[13]||(e[13]=c("span",{class:"dialog_history"},"历史信息",-1)),c("span",T,[v(y,{onChange:ia,modelValue:na.date,"onUpdate:modelValue":e[6]||(e[6]=a=>na.date=a),type:"date",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",placeholder:"选择时间"},null,8,["modelValue"])])])),footer:p((()=>[c("div",J,[v(k,{onClick:e[8]||(e[8]=a=>O.value=!1)},{default:p((()=>e[39]||(e[39]=[_("取消")]))),_:1}),v(k,{type:"primary"},{default:p((()=>e[40]||(e[40]=[_(" 打印 ")]))),_:1})])])),default:p((()=>[c("div",q,[c("div",B,[e[14]||(e[14]=c("label",null,"当前账号",-1)),c("span",null,h(na.data.cashdesk_user),1)]),c("div",H,[e[15]||(e[15]=c("label",null,"登录时间",-1)),c("span",null,h(na.data.logintime),1)]),c("div",L,[e[16]||(e[16]=c("label",null,"交班时间",-1)),c("span",null,h(na.data.jiaobantime),1)]),e[24]||(e[24]=c("hr",null,null,-1)),c("div",S,[e[17]||(e[17]=c("label",null,"订单总数",-1)),c("span",null,h(na.data.ordercount),1)]),e[25]||(e[25]=c("div",{class:"handover_item"},[c("label",null,"进店人数"),c("span",null,h(112211211))],-1)),e[26]||(e[26]=c("div",{class:"handover_item"},[c("label",null,"总商品成本"),c("span",null,h(112211211))],-1)),e[27]||(e[27]=c("div",{class:"handover_item"},[c("label",null,"毛利润"),c("span",null,h(112211211))],-1)),e[28]||(e[28]=c("div",{class:"handover_item"},[c("label",null,"人均消费"),c("span",null,h(112211211))],-1)),e[29]||(e[29]=c("div",{class:"handover_item"},[c("label",null,"折扣"),c("span",null,h(112211211))],-1)),e[30]||(e[30]=c("div",{class:"handover_item"},[c("label",null,"抹零"),c("span",null,h(112211211))],-1)),e[31]||(e[31]=c("hr",null,null,-1)),c("div",U,[e[18]||(e[18]=c("label",null,"今日营业额总收款",-1)),c("span",null,h(na.data.today_total_money),1)]),c("div",F,[e[19]||(e[19]=c("label",null,"现金",-1)),c("span",null,h(na.data.today_cash_money),1)]),c("div",N,[e[20]||(e[20]=c("label",null,"余额",-1)),c("span",null,h(na.data.today_yue_money),1)]),e[32]||(e[32]=c("div",{class:"handover_item"},[c("label",null,"本店储值"),c("span",null,h(112211211))],-1)),c("div",A,[e[21]||(e[21]=c("label",null,"微信",-1)),c("span",null,h(na.data.today_wx_money),1)]),c("div",Q,[e[22]||(e[22]=c("label",null,"支付宝",-1)),c("span",null,h(na.data.today_alipay_money),1)]),e[33]||(e[33]=c("div",{class:"handover_item"},[c("label",null,"QR"),c("span",null,h(112211211))],-1)),e[34]||(e[34]=c("hr",null,null,-1)),e[35]||(e[35]=c("div",{class:"handover_item"},[c("label",null,"今日储值"),c("span",null,h(112211211))],-1)),e[36]||(e[36]=c("div",{class:"handover_item"},[c("label",null,"消费"),c("span",null,h(112211211))],-1)),c("div",Z,[e[23]||(e[23]=c("label",null,"退款总额",-1)),c("span",null,h(na.data.today_refund_total_money),1)]),e[37]||(e[37]=c("hr",null,null,-1)),e[38]||(e[38]=c("div",{class:"handover_item"},[c("label",null,"今日总收款"),c("span",{class:"total"},h(112211211))],-1)),c("div",G,[v(w,{modelValue:na.print,"onUpdate:modelValue":e[7]||(e[7]=a=>na.print=a),size:"large","active-text":"打印小票"},null,8,["modelValue"])])])])),_:1},8,["modelValue"])],64)}}});K.__scopeId="data-v-254aa0c0";export{K as default};
