let a=document.createElement("style");a.innerHTML=".bg-green[data-v-4b7e4a31]{background-color:#15bc84}.bg-orange[data-v-4b7e4a31]{background-color:#fd943e}.bg-blue[data-v-4b7e4a31]{background-color:#007aff}.tables[data-v-4b7e4a31]{min-height:100%;height:100%;background-color:#fff;padding:20px;box-sizing:border-box}.tables .inner[data-v-4b7e4a31]{display:flex;column-gap:20px;row-gap:20px;flex-wrap:wrap}.tables .inner .table_item[data-v-4b7e4a31]{width:150px;height:150px;color:#fff;border-radius:10px;display:flex;flex-direction:column;justify-content:center;align-items:center;cursor:pointer}",document.head.appendChild(a);import{d as e,r as t,o as s,b as n,e as r,f as o,F as i,z as l,j as d,A as c,t as b,i as p,y as u}from"./index.87a9fe81.js";import{a as g}from"./api.bd37d314.js";import{s as f}from"./utils.9f6c7cc3.js";const m={class:"tables"},x={class:"inner"},v=["onClick"],h={style:{color:"black","font-weight":"bold","margin-bottom":"5px"}},k={key:1},y={key:2},w={key:3};var _=e({__name:"index",setup(a){const e=t([]);s((()=>{g().then((a=>{1==a.status&&(e.value=a.datalist)}))}));const _=n();return(a,t)=>(d(),r("div",m,[o("div",x,[(d(!0),r(i,null,l(e.value,((a,e)=>(d(),r("div",{onClick:e=>{return t=`/index/index?tid=${a.id}`,void _.replace(t);var t},class:c(["table_item bg-green",{"bg-orange":2==a?.status,"bg-blue":3==a?.status}]),key:e},[o("span",h,"桌号: "+b(a.name),1),o("span",null,"座位: "+b(2==a.status?(a.order.renshu||1)+"/":"")+b(a.seat),1),2==a.status?(d(),r(i,{key:0},[o("span",null,"价格: "+b(a.order.totalprice),1),o("span",null,"用时: "+b(p(f)(a.order.createtime?((new Date).getTime()/1e3-a.order.createtime).toFixed(0):0)),1)],64)):u("",!0),0==a.status?(d(),r("span",k,"空闲")):u("",!0),2==a.status?(d(),r("span",y,"用餐")):u("",!0),3==a.status?(d(),r("span",w,"清台")):u("",!0)],10,v)))),128))])]))}});_.__scopeId="data-v-4b7e4a31";export{_ as default};
