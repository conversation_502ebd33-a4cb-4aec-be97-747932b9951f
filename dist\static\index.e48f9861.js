let e=document.createElement("style");e.innerHTML=".content[data-v-6de87ea2]{position:relative;height:100%;font-size:14px}.inventory[data-v-6de87ea2]{position:relative;width:520px;height:100%;flex-direction:column;background:#fff}.inventory .inventory_head[data-v-6de87ea2]{color:#333;height:50px;border-bottom:1px solid #f2f2f2;padding:0 16px}.inventory .inventory_head .inventory_title[data-v-6de87ea2]{font-weight:700}.inventory .search[data-v-6de87ea2]{position:relative;box-sizing:border-box}.inventory .search_module[data-v-6de87ea2]{position:relative;margin:16px;width:100%}.inventory .search_icon[data-v-6de87ea2]{position:absolute;top:0;bottom:0;left:10px;margin:auto 0}.inventory .search_input[data-v-6de87ea2]{color:#333;padding:4px 7px 4px 40px;width:100%;border:none;background:#f2f3f5;border-radius:4px;box-sizing:border-box;height:40px}.inventory .body[data-v-6de87ea2]{padding-bottom:20px;overflow:hidden}.inventory .body_module[data-v-6de87ea2]{height:100%;overflow-y:scroll}.inventory .body_null[data-v-6de87ea2]{margin-top:250px}.inventory .body_icon[data-v-6de87ea2]{height:110px;width:110px;display:block;margin:0 auto}.inventory .body_title[data-v-6de87ea2]{text-align:center;font-size:14px;font-weight:500;color:#b2b2b2;margin-top:30px}.inventory .module[data-v-6de87ea2]{padding:10px 16px;color:#333;cursor:pointer;border-bottom:1px solid #f2f2f2}.inventory .module .module_head[data-v-6de87ea2]{height:38px}.inventory .module .module_state[data-v-6de87ea2]{color:#4476ff}.inventory .module .module_item[data-v-6de87ea2]{padding:10px 0}.inventory .module .module_image[data-v-6de87ea2]{width:68px;height:68px;flex-shrink:0;border-radius:4px;margin-right:12px}.inventory .module .module_image .image-slot[data-v-6de87ea2]{display:flex;justify-content:center;align-items:center;width:100%;height:100%;background:var(--el-fill-color-light);color:var(--el-text-color-secondary);font-size:30px}.inventory .module .module_image .image-slot .el-icon[data-v-6de87ea2]{font-size:30px}.inventory .module .module_title[data-v-6de87ea2]{height:50px}.inventory .module .module_num[data-v-6de87ea2]{font-size:14px;color:#999;text-align:right}.operate[data-v-6de87ea2]{position:relative;margin-left:20px;height:100%;background:#fff}.update[data-v-6de87ea2]{position:relative;height:100%;flex-direction:column}.update .update_title[data-v-6de87ea2]{color:#333;line-height:50px;padding:0 16px;font-weight:700;border-bottom:1px solid #f2f2f2}.update .update_notice[data-v-6de87ea2]{padding:150px 0 0;width:100%}.update .update_notice img[data-v-6de87ea2]{height:110px;width:110px;display:block;margin:0 auto}.update .update_notice .text[data-v-6de87ea2]{text-align:center;font-size:14px;font-weight:500;color:#b2b2b2;margin-top:30px}.update .update_icon[data-v-6de87ea2]{height:110px;width:110px;display:block;margin:0 auto}.update .update_text[data-v-6de87ea2]{text-align:center;font-size:14px;font-weight:500;color:#b2b2b2;margin-top:30px}.update .update_body[data-v-6de87ea2]{position:relative;overflow:auto}.update .update_head[data-v-6de87ea2]{position:relative;height:80px;padding-left:20px;font-size:20px;font-weight:700;overflow:hidden}.update .update_wait[data-v-6de87ea2]{color:#f1495c;background-color:#fee8ea}.update .update_finish[data-v-6de87ea2]{color:#4476ff;background-color:#eaeeff}.update .update_head img[data-v-6de87ea2]{height:160%;margin-right:-30px;display:block}.update .update_module[data-v-6de87ea2]{padding:20px}.update .update_list[data-v-6de87ea2]{margin-top:20px;width:100%}.update .update_list .order_price[data-v-6de87ea2]{display:block;width:80px;text-align:right}.update .update_list[data-v-6de87ea2]:first-child{margin-top:0}.update .update_item[data-v-6de87ea2]{padding:20px;border-bottom:1px solid #f2f2f2}.update .update_image[data-v-6de87ea2]{width:68px;height:68px;border-radius:4px;margin-right:12px}.update .update_image .image-slot[data-v-6de87ea2]{display:flex;justify-content:center;align-items:center;width:100%;height:100%;background:var(--el-fill-color-light);color:var(--el-text-color-secondary);font-size:30px}.update .update_image .image-slot .el-icon[data-v-6de87ea2]{font-size:30px}.update .update_name[data-v-6de87ea2]{color:#333}.update .update_specs[data-v-6de87ea2]{font-size:14px;color:#333}.update .update_num[data-v-6de87ea2]{color:#999;font-size:14px;margin-top:15px}.update .update_remarks[data-v-6de87ea2]{border-top:1px solid #f2f2f2;border-bottom:1px solid #f2f2f2;padding:20px 0;margin-top:20px}.update .update_lable[data-v-6de87ea2]{width:320px}.update .update_unit[data-v-6de87ea2]{width:120px;text-align:right}.update .update_opt[data-v-6de87ea2]{padding:8px;box-sizing:border-box;border-top:1px solid #f2f2f2}.update .update_opt .update_btn[data-v-6de87ea2]{height:48px;width:90px;font-size:14px;border:1px solid;border-radius:4px;margin-left:10px;cursor:pointer}",document.head.appendChild(e);import{d as a,a as t,b as n,r as o,o as d,s as i,E as l,c as r,e as s,f as v,g as k,h as p,B as u,C as c,n as f,i as m,F as x,z as h,y as g,t as y,k as _,j as b,I as A}from"./index.87a9fe81.js";import{_ as I}from"./order.b10cb578.js";import{a as w,o as R,E as T,c as B,p as C,F as S}from"./api.bd37d314.js";import{u as Q}from"./userInfo.76997c16.js";import{g as F,d as J}from"./utils.9f6c7cc3.js";const M={class:"content flex_y_center"},Y={class:"inventory flex"},W={class:"search flex_y_center"},z={class:"search_module"},j={class:"body flex_y1"},E={class:"body_module"},D=["onClick"],q={class:"module_head flex_y_center flex_bt"},K={class:"module_state"},N={class:"image-slot"},U={class:"flex_x1"},G={class:"module_title"},H={class:"module_num"},Z={class:"module_head flex_y_center flex_bt"},O={key:0,class:"body_null"},P={class:"operate flex_x1"},V={class:"update flex"},L={key:0,class:"update_body flex_y1"},X={key:0,class:"update_head flex_y_center flex_bt update_wait"},$={key:1,class:"update_head flex_y_center flex_bt update_finish"},ee={class:"update_module"},ae={class:"update_list"},te={class:"update_list"},ne={class:"image-slot"},oe={class:"flex_x1"},de={class:"update_name flex flex_x1"},ie={class:"flex_x1"},le={class:"update_data"},re={class:"update_num flex"},se={class:"update_specs flex_x1"},ve={class:"update_module"},ke={class:"update_list flex"},pe={class:"update_lable"},ue={class:"update_list flex"},ce={class:"update_lable"},fe={class:"update_remarks"},me={class:"update_list flex flex_x_bottom"},xe={class:"update_list flex flex_x_bottom"},he={class:"update_list flex flex_x_bottom"},ge={class:"update_list flex flex_x_bottom"},ye={class:"update_list flex flex_x_bottom"},_e={key:1,class:"update_opt flex flex_x_bottom"},be={key:2,class:"update_notice"},Ae={class:"dialog-footer"};var Ie=a({__name:"index",setup(e){const a=F(),Ie=t({dataList:[],orderInfo:"",searchFocus:"",remark:"",keyword:"",remarkState:!1}),we={0:"未付款",1:"已付款",2:"已发货",3:"已收货"},Re=Q(),Te=n(),Be=o([]);d((()=>{if(Te.currentRoute.value.query.id){let e=Te.currentRoute.value.query.id;sessionStorage.setItem("cashierId",e)}Je(),Me(),w().then((e=>{1==e.status&&(Be.value=e.datalist)}))}));const Ce=()=>{R();let e={apifrom:"vue",orderid:Ie.orderInfo.id};C(e).then((e=>{B(),"1"==e.status?l.success("已打印"):l.error(e.msg)}))},Se=()=>{if(Ie.remark){R();let e={apifrom:"vue",orderid:Ie.orderInfo.id,remark:Ie.remark};S(e).then((e=>{B(),"1"==e.status?(Ie.orderInfo.remark=Ie.remark,Ie.remark="",Ie.remarkState=!1,l({message:"操作成功",type:"success"})):l.error(e.msg)}))}else l.error("请输入备注")};i((()=>{document.onkeydown=null}));const Qe=()=>{Ie.searchFocus="1"},Fe=()=>{Ie.searchFocus="0"},Je=()=>{R();let e={bid:Re.userInfo.bid,key:Ie.keyword};T(e).then((e=>{B(),"1"==e.status?Ie.dataList=e.data:l.error(e.msg)}))},Me=()=>{document.onkeydown=function(e){"Enter"==e.key&&1==Ie.searchFocus&&Je()}};return(e,t)=>{const n=r("Search"),o=r("el-icon"),d=r("el-image"),i=r("el-input"),l=r("el-form-item"),w=r("el-form"),R=r("el-button"),T=r("el-dialog");return b(),s("div",M,[v("div",Y,[t[6]||(t[6]=v("div",{class:"inventory_head flex_y_center flex_bt"},[v("span",{class:"inventory_title"},"订单管理")],-1)),v("div",W,[v("div",z,[k(o,{class:"search_icon",color:"#808695",size:20},{default:p((()=>[k(n)])),_:1}),u(v("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=e=>Ie.keyword=e),onFocusin:Qe,onFocusout:Fe,class:"search_input flex_x1",style:f(1==Ie.searchFocus?"outline: 1px solid"+m(Re).userInfo.color1:""),placeholder:"输入商品条形码或商品名称"},null,36),[[c,Ie.keyword]])])]),v("div",j,[v("div",E,[(b(!0),s(x,null,h(Ie.dataList,((e,t)=>(b(),s("div",{class:"module",onClick:a=>{return t=e,void(Ie.orderInfo=t);var t},key:t},[v("div",q,[v("span",null,"订单编号："+y(e.ordernum),1),v("span",K,y(we[e.status]),1)]),(b(!0),s(x,null,h(e.prolist,((e,a)=>(b(),s("div",{key:a,class:"module_item flex"},[k(d,{class:"module_image",src:e.pic},{error:p((()=>[v("div",N,[k(o,null,{default:p((()=>[k(m(A))])),_:1})])])),_:2},1032,["src"]),v("div",U,[v("div",G,y(e.name),1),v("div",H,"数量："+y(e.num),1)])])))),128)),v("div",Z,[v("span",null,"下单时间："+y(m(J)(e.createtime)),1),v("span",null,[_("共"+y(e.prolist.length)+"件，合计：",1),v("span",{style:f("color:"+m(Re).userInfo.color1)},y(m(a)+e.totalprice),5)])])],8,D)))),128)),Ie.dataList.length?g("",!0):(b(),s("div",O,t[5]||(t[5]=[v("img",{src:I,class:"body_icon",alt:""},null,-1),v("div",{class:"body_title"},"未添加任何订单",-1)])))])])]),v("div",P,[v("div",V,[t[16]||(t[16]=v("div",{class:"update_title"},"订单详情",-1)),""!=Ie.orderInfo?(b(),s("div",L,["0"==Ie.orderInfo.status?(b(),s("div",X,t[7]||(t[7]=[v("div",null,"等待付款",-1),v("img",{src:"data:image/png;base64,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",class:"body_icon",alt:""},null,-1)]))):(b(),s("div",$,t[8]||(t[8]=[v("div",null,"已完成",-1),v("img",{src:"data:image/png;base64,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",class:"body_icon",alt:""},null,-1)]))),v("div",ee,[v("div",ae,"买家："+y(Ie.orderInfo.buyer),1),t[9]||(t[9]=v("div",{class:"update_list"},"收银员：操作员（超级管理员）",-1)),v("div",te,"桌号："+y((B=Ie.orderInfo.tableid,Be.value.find((e=>e.id==B)).name)),1)]),(b(!0),s(x,null,h(Ie.orderInfo.prolist,((e,t)=>(b(),s("div",{key:t,class:"update_item flex_y_center"},[k(d,{class:"update_image",src:e.pic},{error:p((()=>[v("div",ne,[k(o,null,{default:p((()=>[k(m(A))])),_:1})])])),_:2},1032,["src"]),v("div",oe,[v("div",de,[v("div",ie,[v("div",le,y(e.name),1)]),v("div",{style:f("color:"+m(Re).userInfo.color1)},y(m(a)+e.sell_price),5)]),v("div",re,[v("div",se," 规格："+y(e.ggname),1),_(" 数量："+y(e.num),1)])])])))),128)),v("div",ve,[v("div",ke,[v("div",pe,"订单编号："+y(Ie.orderInfo.ordernum),1),_("下单时间："+y(m(J)(Ie.orderInfo.createtime)),1)]),v("div",ue,[v("div",ce,"支付时间："+y(Ie.orderInfo.paytime),1),_("支付方式："+y(Ie.orderInfo.paytype),1)]),v("div",fe,"备注："+y(Ie.orderInfo.message),1),v("div",me,[t[10]||(t[10]=v("div",{class:"update_unit"},"商品金额：",-1)),v("span",{class:"order_price",style:f("color:"+m(Re).userInfo.color1+";font-size:16px;")},y(m(a)+Ie.orderInfo.product_price),5)]),v("div",xe,[t[11]||(t[11]=v("div",{class:"update_unit"},"服务费：",-1)),v("span",{class:"order_price",style:f("color:"+m(Re).userInfo.color1+";font-size:16px")},y(m(a)+Ie.orderInfo.server_fee),5)]),v("div",he,[t[12]||(t[12]=v("div",{class:"update_unit"},"茶位费：",-1)),v("span",{class:"order_price",style:f("color:"+m(Re).userInfo.color1+";font-size:16px")},y(m(a)+Ie.orderInfo.tea_fee),5)]),v("div",ge,[t[13]||(t[13]=v("div",{class:"update_unit"},"税金：",-1)),v("span",{class:"order_price",style:f("color:"+m(Re).userInfo.color1+";font-size:16px")},y(m(a)+Ie.orderInfo.taxes),5)]),v("div",ye,[t[14]||(t[14]=v("div",{class:"update_unit"},"实付金额：",-1)),v("span",{class:"order_price",style:f("color:"+m(Re).userInfo.color1+";font-size:16px")},y(m(a)+Ie.orderInfo.totalprice),5)])])])):g("",!0),""!=Ie.orderInfo?(b(),s("div",_e,[v("div",{onClick:Ce,style:f("color:"+m(Re).userInfo.color1+";border-color:"+m(Re).userInfo.color1),class:"update_btn flex_xy_center"},"打印小票",4),v("div",{onClick:t[1]||(t[1]=e=>Ie.remarkState=!0),style:f("color:#fff;background:"+m(Re).userInfo.color1),class:"update_btn flex_xy_center"},"备注",4)])):g("",!0),""==Ie.orderInfo?(b(),s("div",be,t[15]||(t[15]=[v("img",{src:I,alt:""},null,-1),v("div",{class:"text"},"未选中任何订单",-1)]))):g("",!0)])]),k(T,{modelValue:Ie.remarkState,"onUpdate:modelValue":t[4]||(t[4]=e=>Ie.remarkState=e),title:"添加备注",width:"500px"},{footer:p((()=>[v("span",Ae,[k(R,{onClick:t[3]||(t[3]=e=>Ie.remarkState=!1)},{default:p((()=>t[17]||(t[17]=[_("取消")]))),_:1}),k(R,{onClick:Se,type:"primary"},{default:p((()=>t[18]||(t[18]=[_("确定")]))),_:1})])])),default:p((()=>[k(w,{model:Ie},{default:p((()=>[k(l,{label:"备注","label-width":"200"},{default:p((()=>[k(i,{modelValue:Ie.remark,"onUpdate:modelValue":t[2]||(t[2]=e=>Ie.remark=e),autosize:{minRows:6,maxRows:4},maxlength:"200","show-word-limit":"",type:"textarea"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])]);var B}}});Ie.__scopeId="data-v-6de87ea2";export{Ie as default};
