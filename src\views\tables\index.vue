<template>
    <div class="tables">
        <div class="inner">
            <div @click="toPath(`/index/index?tid=${item.id}`)" class="table_item bg-green" :class="{'bg-orange': item?.status == 2,'bg-blue':item?.status == 3}" v-for="(item, index) in tableData" :key="index">
                <span style="color: black;font-weight: bold;margin-bottom: 5px;">桌号: {{item.name}}</span>
                <span>座位: {{item.status == 2 ? ((item.order.renshu || 1) + '/'): ''}}{{item.seat}}</span>
                <template v-if="item.status == 2">
                    <span>价格: {{item.order.totalprice}}</span>
                    <span>用时: {{secondsFormat(item.order.createtime ? (new Date().getTime()/ 1000 - item.order.createtime).toFixed(0): 0)}}</span>
                </template>
                <span v-if="item.status == 0">空闲</span>
                <span v-if="item.status == 2">用餐</span>
                <span v-if="item.status == 3">清台</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { ref, reactive, onMounted, computed } from 'vue';
    import { useRouter } from 'vue-router'
    import { getTableList } from '../../assets/apis/api'
    import { secondsFormat } from '../../utils/utils'
    const tableData:any = ref([]);

    onMounted(() => {
        getTableList().then((res:any) => {
            if(res.status == 1)
                tableData.value = res.datalist;
        })
    })

    const router = useRouter()
    const toPath = (path: any) => {
        router.replace(path)
    }
</script>

<style lang="scss" scoped>
    .bg-green { background-color: #15BC84;}
    .bg-orange { background-color: #FD943E;}
    .bg-blue {background-color: #007AFF;}
    .tables {
        min-height: 100%;
        height: 100%;
        background-color: #fff;
        padding: 20px;
        box-sizing: border-box;
        .inner {
            display: flex;
            column-gap: 20px;
            row-gap: 20px;
            flex-wrap: wrap;
            .table_item {
                width: 150px;
                height: 150px;
                color:#fff;
                border-radius: 10px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                cursor: pointer;
            }
        }
    }
</style>