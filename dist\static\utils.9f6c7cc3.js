import{a as t}from"./index.87a9fe81.js";const e=t([{value:0,text:"¥"},{value:1,text:"NT$"},{value:2,text:"$"},{value:3,text:"₫"},{value:4,text:"฿"},{value:5,text:"₹"},{value:6,text:"RM"}]);function a(){return(e.filter((t=>4==t.value))[0]?.text||"฿")+" "}function r(t){if(!t)return"";if(new String(t).includes("-"))return t;const e=new Date(1e3*t);return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}:${String(e.getSeconds()).padStart(2,"0")}`}function n(t){let e=Math.floor(t/3600),a=t%3600,r=Math.floor(a/60),n=a%60;return`${String(e).padStart(2,"0")}:${String(r).padStart(2,"0")}:${String(n).padStart(2,"0")}`}export{r as d,a as g,n as s};
